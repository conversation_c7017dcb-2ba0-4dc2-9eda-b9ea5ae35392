const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const { auth } = require('../middleware/auth');
const {
  getMyAssignments,
  getMyPostedAssignments,
  updateAssignmentStatus,
  getAssignmentDetails
} = require('../controllers/assignmentController');

// Validation middleware
const statusUpdateValidation = [
  body('status')
    .isIn(['pending', 'accepted', 'in_progress', 'completed', 'cancelled', 'disputed'])
    .withMessage('Invalid status')
];

// Validation error handler
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }
  next();
};

// @route   GET /api/assignments/my-assignments
// @desc    Get freelancer's assignments
// @access  Private (Freelancer only)
router.get('/my-assignments', auth, getMyAssignments);

// @route   GET /api/assignments/my-posts
// @desc    Get company's posted assignments
// @access  Private (Company only)
router.get('/my-posts', auth, getMyPostedAssignments);

// @route   GET /api/assignments/:id
// @desc    Get assignment details
// @access  Private (Assignment participants only)
router.get('/:id', auth, getAssignmentDetails);

// @route   PUT /api/assignments/:id/status
// @desc    Update assignment status
// @access  Private (Assignment participants only)
router.put('/:id/status', auth, statusUpdateValidation, handleValidationErrors, updateAssignmentStatus);

module.exports = router;
