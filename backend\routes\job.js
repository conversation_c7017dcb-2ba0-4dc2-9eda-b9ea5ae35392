const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const { auth, optionalAuth } = require('../middleware/auth');
const {
  getJobs,
  getJobById,
  createJob,
  updateJob,
  deleteJob,
  getMyJobs
} = require('../controllers/jobController');

// Validation middleware
const jobValidation = [
  body('title')
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Title must be 5-200 characters'),
  body('description')
    .trim()
    .isLength({ min: 10, max: 5000 })
    .withMessage('Description must be 10-5000 characters'),
  body('payment_type')
    .isIn(['hourly', 'commission', 'prepaid', 'escrow', 'fixed'])
    .withMessage('Invalid payment type'),
  body('payment_amount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Payment amount must be a positive number'),
  body('hourly_rate')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Hourly rate must be a positive number'),
  body('commission_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('Commission rate must be between 0-100%'),
  body('visibility')
    .optional()
    .isIn(['public', 'private'])
    .withMessage('Visibility must be public or private'),
  body('experience_level')
    .optional()
    .isIn(['entry', 'intermediate', 'expert'])
    .withMessage('Invalid experience level'),
  body('deadline')
    .optional()
    .isISO8601()
    .withMessage('Invalid deadline format')
];

// Validation error handler
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }
  next();
};

// @route   GET /api/jobs
// @desc    Get all jobs with filtering
// @access  Public (with optional auth for private jobs)
router.get('/', optionalAuth, getJobs);

// @route   GET /api/jobs/my-posts
// @desc    Get company's posted jobs
// @access  Private (Company only)
router.get('/my-posts', auth, getMyJobs);

// @route   GET /api/jobs/:id
// @desc    Get single job by ID
// @access  Public (with optional auth for private jobs)
router.get('/:id', optionalAuth, getJobById);

// @route   POST /api/jobs
// @desc    Create new job posting
// @access  Private (Company only)
router.post('/', auth, jobValidation, handleValidationErrors, createJob);

// @route   PUT /api/jobs/:id
// @desc    Update job posting
// @access  Private (Company owner only)
router.put('/:id', auth, jobValidation, handleValidationErrors, updateJob);

// @route   DELETE /api/jobs/:id
// @desc    Delete job posting
// @access  Private (Company owner only)
router.delete('/:id', auth, deleteJob);

module.exports = router;
