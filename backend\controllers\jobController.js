const { Op } = require('sequelize');
const Job = require('../models/Job');
const Application = require('../models/Application');
const User = require('../models/User');

// @desc    Get all jobs with filtering and pagination
// @route   GET /api/jobs
// @access  Public
const getJobs = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      location,
      payment_type,
      experience_level,
      status = 'active',
      visibility = 'public',
      company_id
    } = req.query;

    const offset = (page - 1) * limit;
    
    // Build where clause
    const whereClause = {};
    
    // Only show active jobs by default for public access
    if (status) {
      whereClause.status = status;
    }
    
    // Handle visibility - if user is not logged in, only show public jobs
    if (!req.user) {
      whereClause.visibility = 'public';
    } else if (visibility) {
      whereClause.visibility = visibility;
    }
    
    // Filter by company if specified
    if (company_id) {
      whereClause.company_id = company_id;
    }
    
    // Search functionality
    if (search) {
      whereClause[Op.or] = [
        { title: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } }
      ];
    }
    
    // Location filter
    if (location) {
      whereClause.location = { [Op.iLike]: `%${location}%` };
    }
    
    // Payment type filter
    if (payment_type) {
      whereClause.payment_type = payment_type;
    }
    
    // Experience level filter
    if (experience_level) {
      whereClause.experience_level = experience_level;
    }

    const jobs = await Job.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'company',
          attributes: ['id', 'company_name', 'profile_image']
        }
      ],
      order: [
        ['is_promoted', 'DESC'],
        ['created_at', 'DESC']
      ],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        jobs: jobs.rows.map(job => ({
          ...job.getPublicData(),
          company: job.company
        })),
        pagination: {
          current_page: parseInt(page),
          total_pages: Math.ceil(jobs.count / limit),
          total_jobs: jobs.count,
          per_page: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Get jobs error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch jobs'
    });
  }
};

// @desc    Get single job by ID
// @route   GET /api/jobs/:id
// @access  Public
const getJobById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const job = await Job.findByPk(id, {
      include: [
        {
          model: User,
          as: 'company',
          attributes: ['id', 'company_name', 'profile_image', 'bio']
        }
      ]
    });

    if (!job) {
      return res.status(404).json({
        success: false,
        message: 'Job not found'
      });
    }

    // Check visibility permissions
    if (job.visibility === 'private' && (!req.user || req.user.role !== 'freelancer')) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Login required to view this job.'
      });
    }

    // Increment view count
    await job.incrementViewCount();

    // Check if current user has applied (if logged in)
    let hasApplied = false;
    if (req.user && req.user.role === 'freelancer') {
      const application = await Application.findOne({
        where: {
          job_id: job.id,
          freelancer_id: req.user.id
        }
      });
      hasApplied = !!application;
    }

    res.json({
      success: true,
      data: {
        job: {
          ...job.getPublicData(),
          company: job.company,
          has_applied: hasApplied
        }
      }
    });

  } catch (error) {
    console.error('Get job by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch job'
    });
  }
};

// @desc    Create new job posting
// @route   POST /api/jobs
// @access  Private (Company only)
const createJob = async (req, res) => {
  try {
    const {
      title,
      description,
      location,
      payment_type,
      payment_amount,
      hourly_rate,
      commission_rate,
      visibility = 'public',
      login_required_to_apply = true,
      skills_required = [],
      experience_level,
      deadline
    } = req.body;

    // Validate company role
    if (req.user.role !== 'company') {
      return res.status(403).json({
        success: false,
        message: 'Only companies can post jobs'
      });
    }

    const job = await Job.create({
      company_id: req.user.id,
      title,
      description,
      location,
      payment_type,
      payment_amount,
      hourly_rate,
      commission_rate,
      visibility,
      login_required_to_apply,
      skills_required,
      experience_level,
      deadline: deadline ? new Date(deadline) : null,
      status: 'active'
    });

    res.status(201).json({
      success: true,
      message: 'Job posted successfully',
      data: { job: job.getPublicData() }
    });

  } catch (error) {
    console.error('Create job error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create job posting'
    });
  }
};

// @desc    Update job posting
// @route   PUT /api/jobs/:id
// @access  Private (Company owner only)
const updateJob = async (req, res) => {
  try {
    const { id } = req.params;
    const job = await Job.findByPk(id);

    if (!job) {
      return res.status(404).json({
        success: false,
        message: 'Job not found'
      });
    }

    // Check ownership
    if (job.company_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const updatedJob = await job.update(req.body);

    res.json({
      success: true,
      message: 'Job updated successfully',
      data: { job: updatedJob.getPublicData() }
    });

  } catch (error) {
    console.error('Update job error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update job'
    });
  }
};

// @desc    Delete job posting
// @route   DELETE /api/jobs/:id
// @access  Private (Company owner only)
const deleteJob = async (req, res) => {
  try {
    const { id } = req.params;
    const job = await Job.findByPk(id);

    if (!job) {
      return res.status(404).json({
        success: false,
        message: 'Job not found'
      });
    }

    // Check ownership
    if (job.company_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    await job.destroy();

    res.json({
      success: true,
      message: 'Job deleted successfully'
    });

  } catch (error) {
    console.error('Delete job error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete job'
    });
  }
};

// @desc    Get company's posted jobs
// @route   GET /api/jobs/my-posts
// @access  Private (Company only)
const getMyJobs = async (req, res) => {
  try {
    if (req.user.role !== 'company') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const jobs = await Job.findAll({
      where: { company_id: req.user.id },
      include: [
        {
          model: Application,
          as: 'applications',
          include: [
            {
              model: User,
              as: 'freelancer',
              attributes: ['id', 'first_name', 'last_name', 'profile_image']
            }
          ]
        }
      ],
      order: [['created_at', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        jobs: jobs.map(job => ({
          ...job.getPublicData(),
          applications: job.applications.map(app => app.getPublicData())
        }))
      }
    });

  } catch (error) {
    console.error('Get my jobs error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch your jobs'
    });
  }
};

module.exports = {
  getJobs,
  getJobById,
  createJob,
  updateJob,
  deleteJob,
  getMyJobs
};
