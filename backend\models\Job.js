const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Job = sequelize.define('Job', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  company_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      len: [5, 200]
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: false,
    validate: {
      len: [10, 5000]
    }
  },
  location: {
    type: DataTypes.STRING,
    allowNull: true
  },
  payment_type: {
    type: DataTypes.ENUM(
      'hourly',
      'commission',
      'prepaid',
      'escrow',
      'fixed'
    ),
    allowNull: false,
    defaultValue: 'fixed'
  },
  payment_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  payment_currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'EUR'
  },
  hourly_rate: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  commission_rate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  visibility: {
    type: DataTypes.ENUM('public', 'private'),
    allowNull: false,
    defaultValue: 'public'
  },
  login_required_to_apply: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  is_promoted: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  promotion_expires_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM(
      'draft',
      'active',
      'paused',
      'closed',
      'filled'
    ),
    defaultValue: 'draft'
  },
  skills_required: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  experience_level: {
    type: DataTypes.ENUM(
      'entry',
      'intermediate',
      'expert'
    ),
    allowNull: true
  },
  deadline: {
    type: DataTypes.DATE,
    allowNull: true
  },
  application_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  view_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  }
}, {
  tableName: 'jobs',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Instance methods
Job.prototype.getPublicData = function() {
  return {
    id: this.id,
    company_id: this.company_id,
    title: this.title,
    description: this.description,
    location: this.location,
    payment_type: this.payment_type,
    payment_amount: this.payment_amount,
    payment_currency: this.payment_currency,
    hourly_rate: this.hourly_rate,
    commission_rate: this.commission_rate,
    visibility: this.visibility,
    login_required_to_apply: this.login_required_to_apply,
    is_promoted: this.is_promoted,
    status: this.status,
    skills_required: this.skills_required,
    experience_level: this.experience_level,
    deadline: this.deadline,
    application_count: this.application_count,
    view_count: this.view_count,
    created_at: this.created_at,
    updated_at: this.updated_at
  };
};

Job.prototype.incrementViewCount = async function() {
  this.view_count += 1;
  await this.save();
};

Job.prototype.incrementApplicationCount = async function() {
  this.application_count += 1;
  await this.save();
};

module.exports = Job;
