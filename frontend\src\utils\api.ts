import axios, { AxiosResponse } from 'axios';
import Cookies from 'js-cookie';
import { toast } from 'react-hot-toast';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = Cookies.get('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      Cookies.remove('token');
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
    }
    
    // Show error toast for non-401 errors
    if (error.response?.status !== 401) {
      const message = error.response?.data?.message || 'An error occurred';
      toast.error(message);
    }
    
    return Promise.reject(error);
  }
);

export default api;

// API endpoints
export const authAPI = {
  register: (data: any) => api.post('/api/auth/register', data),
  login: (data: any) => api.post('/api/auth/login', data),
  logout: () => api.post('/api/auth/logout'),
  getMe: () => api.get('/api/auth/me'),
  verifyEmail: (token: string) => api.get(`/api/auth/verify/${token}`),
};

export const userAPI = {
  getProfile: () => api.get('/api/user/profile'),
  updateProfile: (data: any) => api.put('/api/user/profile', data),
  uploadImage: (formData: FormData) => api.post('/api/user/upload-image', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
  deleteAccount: () => api.delete('/api/user/account'),
  getReferralStats: () => api.get('/api/user/referral-stats'),
};

export const paymentAPI = {
  getConfig: () => api.get('/api/payment/config'),
  createCustomer: () => api.post('/api/payment/create-customer'),
  createOrder: (data: any) => api.post('/api/payment/create-order', data),
  createPaymentIntent: (data: any) => api.post('/api/payment/create-payment-intent', data),
};

export const jobAPI = {
  getJobs: (params?: any) => api.get('/api/jobs', { params }),
  getJobById: (id: string) => api.get(`/api/jobs/${id}`),
  createJob: (data: any) => api.post('/api/jobs', data),
  updateJob: (id: string, data: any) => api.put(`/api/jobs/${id}`, data),
  deleteJob: (id: string) => api.delete(`/api/jobs/${id}`),
  getMyJobs: () => api.get('/api/jobs/my-posts'),
};

export const applicationAPI = {
  applyToJob: (data: any) => api.post('/api/applications', data),
  getMyApplications: () => api.get('/api/applications/my-applications'),
  getJobApplications: (jobId: string) => api.get(`/api/applications/job/${jobId}`),
  updateApplicationStatus: (id: string, data: any) => api.put(`/api/applications/${id}/status`, data),
  withdrawApplication: (id: string) => api.delete(`/api/applications/${id}`),
};

export const assignmentAPI = {
  getMyAssignments: () => api.get('/api/assignments/my-assignments'),
  getMyPostedAssignments: () => api.get('/api/assignments/my-posts'),
  getAssignmentDetails: (id: string) => api.get(`/api/assignments/${id}`),
  updateAssignmentStatus: (id: string, data: any) => api.put(`/api/assignments/${id}/status`, data),
};
