import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { assignmentAPI, applicationAPI } from '@/utils/api';
import toast from 'react-hot-toast';
import { 
  FiClock, 
  FiDollarSign, 
  FiMessageCircle, 
  FiEye, 
  FiCheckCircle, 
  FiXCircle,
  FiPlay,
  FiAlertCircle
} from 'react-icons/fi';

interface Assignment {
  id: string;
  title: string;
  description: string;
  amount: number;
  currency: string;
  status: string;
  payment_status: string;
  created_at: string;
  completed_at?: string;
  delivery_date?: string;
  client?: {
    id: string;
    company_name: string;
    profile_image?: string;
  };
  freelancer?: {
    id: string;
    first_name: string;
    last_name: string;
    profile_image?: string;
  };
  job?: {
    id: string;
    title: string;
    payment_type: string;
  };
}

interface Application {
  id: string;
  status: string;
  created_at: string;
  job: {
    id: string;
    title: string;
    payment_type: string;
    company: {
      id: string;
      company_name: string;
      profile_image?: string;
    };
  };
}

const AssignmentsPage: React.FC = () => {
  const { user } = useAuth();
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'assignments' | 'applications'>('assignments');
  const [updatingStatus, setUpdatingStatus] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      fetchData();
    }
  }, [user]);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      if (user?.role === 'freelancer') {
        // Fetch both assignments and applications for freelancers
        const [assignmentsRes, applicationsRes] = await Promise.all([
          assignmentAPI.getMyAssignments(),
          applicationAPI.getMyApplications()
        ]);
        setAssignments(assignmentsRes.data.data.assignments);
        setApplications(applicationsRes.data.data.applications);
      } else if (user?.role === 'company') {
        // Fetch posted assignments for companies
        const assignmentsRes = await assignmentAPI.getMyPostedAssignments();
        setAssignments(assignmentsRes.data.data.assignments);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to load assignments');
    } finally {
      setLoading(false);
    }
  };

  const updateAssignmentStatus = async (assignmentId: string, newStatus: string) => {
    try {
      setUpdatingStatus(assignmentId);
      await assignmentAPI.updateAssignmentStatus(assignmentId, { status: newStatus });
      
      // Update local state
      setAssignments(assignments.map(assignment => 
        assignment.id === assignmentId 
          ? { ...assignment, status: newStatus }
          : assignment
      ));
      
      toast.success('Assignment status updated successfully');
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to update status');
    } finally {
      setUpdatingStatus(null);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'warning', icon: FiClock, text: 'Pending' },
      accepted: { color: 'info', icon: FiCheckCircle, text: 'Accepted' },
      in_progress: { color: 'primary', icon: FiPlay, text: 'In Progress' },
      completed: { color: 'success', icon: FiCheckCircle, text: 'Completed' },
      cancelled: { color: 'danger', icon: FiXCircle, text: 'Cancelled' },
      disputed: { color: 'danger', icon: FiAlertCircle, text: 'Disputed' }
    };

    const config = statusConfig[status] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <span className={`badge bg-${config.color}`}>
        <Icon className="me-1" size={12} />
        {config.text}
      </span>
    );
  };

  const getImageUrl = (profileImage?: string, userType: 'company' | 'freelancer' = 'freelancer') => {
    if (!profileImage || profileImage === 'default.png') {
      return '/images/default-avatar.png';
    }
    if (profileImage.includes('cloudinary.com')) {
      return profileImage;
    }
    const folder = userType === 'company' ? 'business' : 'sales';
    return `/assets/img/${folder}/${profileImage}`;
  };

  if (!user) {
    return (
      <>
        <Head>
          <title>Assignments - DealClosed Partner</title>
        </Head>
        <Header />
        <div className="container py-5 text-center">
          <h2>Please login to view assignments</h2>
          <Link href="/login" className="btn btn-primary">
            Login
          </Link>
        </div>
        <Footer />
      </>
    );
  }

  return (
    <>
      <Head>
        <title>
          {user.role === 'freelancer' ? 'My Assignments' : 'Posted Assignments'} - DealClosed Partner
        </title>
      </Head>

      <Header />

      <div className="container py-5">
        <div className="row">
          <div className="col-12">
            <h1 className="h2 mb-4">
              {user.role === 'freelancer' ? 'My Assignments' : 'Posted Assignments'}
            </h1>

            {/* Tabs for Freelancers */}
            {user.role === 'freelancer' && (
              <ul className="nav nav-tabs mb-4">
                <li className="nav-item">
                  <button
                    className={`nav-link ${activeTab === 'assignments' ? 'active' : ''}`}
                    onClick={() => setActiveTab('assignments')}
                  >
                    Active Assignments ({assignments.length})
                  </button>
                </li>
                <li className="nav-item">
                  <button
                    className={`nav-link ${activeTab === 'applications' ? 'active' : ''}`}
                    onClick={() => setActiveTab('applications')}
                  >
                    Applications ({applications.length})
                  </button>
                </li>
              </ul>
            )}

            {loading ? (
              <div className="text-center py-5">
                <div className="spinner-border text-success" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
              </div>
            ) : (
              <>
                {/* Assignments Tab */}
                {(activeTab === 'assignments' || user.role === 'company') && (
                  <div>
                    {assignments.length === 0 ? (
                      <div className="text-center py-5">
                        <h4>No assignments found</h4>
                        <p className="text-muted">
                          {user.role === 'freelancer' 
                            ? 'You haven\'t been assigned any projects yet. Browse available jobs to get started!'
                            : 'You haven\'t posted any assignments yet. Create a job posting to find freelancers!'
                          }
                        </p>
                        <Link 
                          href={user.role === 'freelancer' ? '/jobs' : '/post-job'} 
                          className="btn btn-primary"
                        >
                          {user.role === 'freelancer' ? 'Browse Jobs' : 'Post a Job'}
                        </Link>
                      </div>
                    ) : (
                      <div className="row">
                        {assignments.map((assignment) => (
                          <div key={assignment.id} className="col-12 mb-4">
                            <div className="card">
                              <div className="card-body">
                                <div className="row">
                                  <div className="col-md-8">
                                    <div className="d-flex align-items-start mb-3">
                                      {user.role === 'freelancer' && assignment.client && (
                                        <img
                                          src={getImageUrl(assignment.client.profile_image, 'company')}
                                          alt={assignment.client.company_name}
                                          className="rounded-circle me-3"
                                          style={{ width: '50px', height: '50px', objectFit: 'cover' }}
                                        />
                                      )}
                                      {user.role === 'company' && assignment.freelancer && (
                                        <img
                                          src={getImageUrl(assignment.freelancer.profile_image, 'freelancer')}
                                          alt={`${assignment.freelancer.first_name} ${assignment.freelancer.last_name}`}
                                          className="rounded-circle me-3"
                                          style={{ width: '50px', height: '50px', objectFit: 'cover' }}
                                        />
                                      )}
                                      <div className="flex-grow-1">
                                        <h5 className="card-title mb-1">{assignment.title}</h5>
                                        <p className="text-muted mb-2">
                                          {user.role === 'freelancer' && assignment.client
                                            ? assignment.client.company_name
                                            : user.role === 'company' && assignment.freelancer
                                            ? `${assignment.freelancer.first_name} ${assignment.freelancer.last_name}`
                                            : 'Unknown'
                                          }
                                        </p>
                                        <div className="d-flex flex-wrap gap-3 text-sm text-muted mb-2">
                                          <span>
                                            <FiDollarSign className="me-1" />
                                            €{assignment.amount}
                                          </span>
                                          <span>
                                            <FiClock className="me-1" />
                                            {new Date(assignment.created_at).toLocaleDateString()}
                                          </span>
                                          {assignment.delivery_date && (
                                            <span>
                                              Due: {new Date(assignment.delivery_date).toLocaleDateString()}
                                            </span>
                                          )}
                                        </div>
                                        <div className="mb-2">
                                          {getStatusBadge(assignment.status)}
                                          <span className="ms-2 badge bg-light text-dark">
                                            Payment: {assignment.payment_status}
                                          </span>
                                        </div>
                                      </div>
                                    </div>
                                    
                                    <p className="card-text text-muted">
                                      {assignment.description.length > 150 
                                        ? `${assignment.description.substring(0, 150)}...` 
                                        : assignment.description
                                      }
                                    </p>
                                  </div>
                                  
                                  <div className="col-md-4 text-md-end">
                                    <div className="d-grid gap-2">
                                      {/* Status Update Buttons */}
                                      {user.role === 'freelancer' && assignment.status === 'pending' && (
                                        <>
                                          <button
                                            className="btn btn-success btn-sm"
                                            onClick={() => updateAssignmentStatus(assignment.id, 'accepted')}
                                            disabled={updatingStatus === assignment.id}
                                          >
                                            Accept
                                          </button>
                                          <button
                                            className="btn btn-outline-danger btn-sm"
                                            onClick={() => updateAssignmentStatus(assignment.id, 'cancelled')}
                                            disabled={updatingStatus === assignment.id}
                                          >
                                            Decline
                                          </button>
                                        </>
                                      )}
                                      
                                      {user.role === 'freelancer' && assignment.status === 'accepted' && (
                                        <button
                                          className="btn btn-primary btn-sm"
                                          onClick={() => updateAssignmentStatus(assignment.id, 'in_progress')}
                                          disabled={updatingStatus === assignment.id}
                                        >
                                          Start Work
                                        </button>
                                      )}
                                      
                                      {user.role === 'freelancer' && assignment.status === 'in_progress' && (
                                        <button
                                          className="btn btn-success btn-sm"
                                          onClick={() => updateAssignmentStatus(assignment.id, 'completed')}
                                          disabled={updatingStatus === assignment.id}
                                        >
                                          Mark Complete
                                        </button>
                                      )}

                                      {/* Message Button */}
                                      <Link 
                                        href={`/inbox?user=${user.role === 'freelancer' ? assignment.client?.id : assignment.freelancer?.id}`}
                                        className="btn btn-outline-primary btn-sm"
                                      >
                                        <FiMessageCircle className="me-1" />
                                        Message
                                      </Link>
                                      
                                      {/* View Details */}
                                      <Link 
                                        href={`/assignments/${assignment.id}`}
                                        className="btn btn-outline-secondary btn-sm"
                                      >
                                        <FiEye className="me-1" />
                                        Details
                                      </Link>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}

                {/* Applications Tab (Freelancers only) */}
                {user.role === 'freelancer' && activeTab === 'applications' && (
                  <div>
                    {applications.length === 0 ? (
                      <div className="text-center py-5">
                        <h4>No applications found</h4>
                        <p className="text-muted">You haven't applied to any jobs yet.</p>
                        <Link href="/jobs" className="btn btn-primary">
                          Browse Jobs
                        </Link>
                      </div>
                    ) : (
                      <div className="row">
                        {applications.map((application) => (
                          <div key={application.id} className="col-12 mb-3">
                            <div className="card">
                              <div className="card-body">
                                <div className="row align-items-center">
                                  <div className="col-md-8">
                                    <div className="d-flex align-items-center mb-2">
                                      <img
                                        src={getImageUrl(application.job.company.profile_image, 'company')}
                                        alt={application.job.company.company_name}
                                        className="rounded-circle me-3"
                                        style={{ width: '40px', height: '40px', objectFit: 'cover' }}
                                      />
                                      <div>
                                        <h6 className="mb-1">
                                          <Link href={`/jobs/${application.job.id}`} className="text-decoration-none">
                                            {application.job.title}
                                          </Link>
                                        </h6>
                                        <small className="text-muted">{application.job.company.company_name}</small>
                                      </div>
                                    </div>
                                    <small className="text-muted">
                                      Applied on {new Date(application.created_at).toLocaleDateString()}
                                    </small>
                                  </div>
                                  <div className="col-md-4 text-md-end">
                                    {getStatusBadge(application.status)}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      <Footer />
    </>
  );
};

export default AssignmentsPage;
