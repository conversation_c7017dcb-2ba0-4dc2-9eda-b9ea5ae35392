const Order = require('../models/Order');
const Job = require('../models/Job');
const Application = require('../models/Application');
const User = require('../models/User');

// @desc    Get freelancer's assignments
// @route   GET /api/assignments/my-assignments
// @access  Private (Freelancer only)
const getMyAssignments = async (req, res) => {
  try {
    if (req.user.role !== 'freelancer') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const assignments = await Order.findAll({
      where: { freelancer_id: req.user.id },
      include: [
        {
          model: User,
          as: 'client',
          attributes: ['id', 'company_name', 'profile_image']
        },
        {
          model: Job,
          as: 'job',
          attributes: ['id', 'title', 'payment_type']
        },
        {
          model: Application,
          as: 'application',
          attributes: ['id', 'created_at']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        assignments: assignments.map(assignment => ({
          ...assignment.getPublicData(),
          client: assignment.client,
          job: assignment.job,
          application: assignment.application
        }))
      }
    });

  } catch (error) {
    console.error('Get my assignments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch assignments'
    });
  }
};

// @desc    Get company's posted assignments
// @route   GET /api/assignments/my-posts
// @access  Private (Company only)
const getMyPostedAssignments = async (req, res) => {
  try {
    if (req.user.role !== 'company') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const assignments = await Order.findAll({
      where: { client_id: req.user.id },
      include: [
        {
          model: User,
          as: 'freelancer',
          attributes: ['id', 'first_name', 'last_name', 'profile_image']
        },
        {
          model: Job,
          as: 'job',
          attributes: ['id', 'title', 'payment_type', 'application_count']
        },
        {
          model: Application,
          as: 'application',
          attributes: ['id', 'created_at']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        assignments: assignments.map(assignment => ({
          ...assignment.getPublicData(),
          freelancer: assignment.freelancer,
          job: assignment.job,
          application: assignment.application
        }))
      }
    });

  } catch (error) {
    console.error('Get my posted assignments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch posted assignments'
    });
  }
};

// @desc    Update assignment status
// @route   PUT /api/assignments/:id/status
// @access  Private (Assignment participants only)
const updateAssignmentStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const assignment = await Order.findByPk(id, {
      include: [
        {
          model: User,
          as: 'client',
          attributes: ['id', 'company_name']
        },
        {
          model: User,
          as: 'freelancer',
          attributes: ['id', 'first_name', 'last_name']
        }
      ]
    });

    if (!assignment) {
      return res.status(404).json({
        success: false,
        message: 'Assignment not found'
      });
    }

    // Check if user is part of this assignment
    const isClient = assignment.client_id === req.user.id;
    const isFreelancer = assignment.freelancer_id === req.user.id;

    if (!isClient && !isFreelancer) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Validate status transitions based on user role
    const validTransitions = {
      freelancer: {
        'pending': ['accepted', 'cancelled'],
        'accepted': ['in_progress', 'cancelled'],
        'in_progress': ['completed', 'cancelled']
      },
      company: {
        'pending': ['cancelled'],
        'accepted': ['cancelled'],
        'in_progress': ['cancelled'],
        'completed': ['cancelled']
      }
    };

    const userRole = isClient ? 'company' : 'freelancer';
    const allowedStatuses = validTransitions[userRole][assignment.status] || [];

    if (!allowedStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: `Cannot change status from ${assignment.status} to ${status}`
      });
    }

    // Update assignment status
    assignment.status = status;
    
    if (status === 'completed') {
      assignment.completed_at = new Date();
    }

    await assignment.save();

    res.json({
      success: true,
      message: 'Assignment status updated successfully',
      data: {
        assignment: {
          ...assignment.getPublicData(),
          client: assignment.client,
          freelancer: assignment.freelancer
        }
      }
    });

  } catch (error) {
    console.error('Update assignment status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update assignment status'
    });
  }
};

// @desc    Get assignment details
// @route   GET /api/assignments/:id
// @access  Private (Assignment participants only)
const getAssignmentDetails = async (req, res) => {
  try {
    const { id } = req.params;

    const assignment = await Order.findByPk(id, {
      include: [
        {
          model: User,
          as: 'client',
          attributes: ['id', 'company_name', 'profile_image', 'email']
        },
        {
          model: User,
          as: 'freelancer',
          attributes: ['id', 'first_name', 'last_name', 'profile_image', 'email']
        },
        {
          model: Job,
          as: 'job'
        },
        {
          model: Application,
          as: 'application'
        }
      ]
    });

    if (!assignment) {
      return res.status(404).json({
        success: false,
        message: 'Assignment not found'
      });
    }

    // Check if user is part of this assignment
    const isClient = assignment.client_id === req.user.id;
    const isFreelancer = assignment.freelancer_id === req.user.id;

    if (!isClient && !isFreelancer) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    res.json({
      success: true,
      data: {
        assignment: {
          ...assignment.getPublicData(),
          client: assignment.client,
          freelancer: assignment.freelancer,
          job: assignment.job ? assignment.job.getPublicData() : null,
          application: assignment.application ? assignment.application.getPublicData() : null
        }
      }
    });

  } catch (error) {
    console.error('Get assignment details error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch assignment details'
    });
  }
};

module.exports = {
  getMyAssignments,
  getMyPostedAssignments,
  updateAssignmentStatus,
  getAssignmentDetails
};
