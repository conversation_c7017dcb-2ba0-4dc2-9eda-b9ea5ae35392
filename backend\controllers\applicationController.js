const Application = require('../models/Application');
const Job = require('../models/Job');
const User = require('../models/User');

// @desc    Apply to a job
// @route   POST /api/applications
// @access  Private (Freelancer only)
const applyToJob = async (req, res) => {
  try {
    const {
      job_id,
      cover_letter,
      proposed_rate,
      proposed_timeline,
      portfolio_links = []
    } = req.body;

    // Validate freelancer role
    if (req.user.role !== 'freelancer') {
      return res.status(403).json({
        success: false,
        message: 'Only freelancers can apply to jobs'
      });
    }

    // Check if job exists and is active
    const job = await Job.findByPk(job_id);
    if (!job) {
      return res.status(404).json({
        success: false,
        message: 'Job not found'
      });
    }

    if (job.status !== 'active') {
      return res.status(400).json({
        success: false,
        message: 'This job is no longer accepting applications'
      });
    }

    // Check if user already applied
    const existingApplication = await Application.findOne({
      where: {
        job_id,
        freelancer_id: req.user.id
      }
    });

    if (existingApplication) {
      return res.status(400).json({
        success: false,
        message: 'You have already applied to this job'
      });
    }

    // Create application
    const application = await Application.create({
      job_id,
      freelancer_id: req.user.id,
      cover_letter,
      proposed_rate,
      proposed_timeline,
      portfolio_links
    });

    // Increment job application count
    await job.incrementApplicationCount();

    res.status(201).json({
      success: true,
      message: 'Application submitted successfully',
      data: { application: application.getPublicData() }
    });

  } catch (error) {
    console.error('Apply to job error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to submit application'
    });
  }
};

// @desc    Get freelancer's applications
// @route   GET /api/applications/my-applications
// @access  Private (Freelancer only)
const getMyApplications = async (req, res) => {
  try {
    if (req.user.role !== 'freelancer') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const applications = await Application.findAll({
      where: { freelancer_id: req.user.id },
      include: [
        {
          model: Job,
          as: 'job',
          include: [
            {
              model: User,
              as: 'company',
              attributes: ['id', 'company_name', 'profile_image']
            }
          ]
        }
      ],
      order: [['created_at', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        applications: applications.map(app => ({
          ...app.getPublicData(),
          job: app.job ? {
            ...app.job.getPublicData(),
            company: app.job.company
          } : null
        }))
      }
    });

  } catch (error) {
    console.error('Get my applications error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch applications'
    });
  }
};

// @desc    Get applications for a specific job
// @route   GET /api/applications/job/:jobId
// @access  Private (Company owner only)
const getJobApplications = async (req, res) => {
  try {
    const { jobId } = req.params;

    // Check if job exists and user owns it
    const job = await Job.findByPk(jobId);
    if (!job) {
      return res.status(404).json({
        success: false,
        message: 'Job not found'
      });
    }

    if (job.company_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const applications = await Application.findAll({
      where: { job_id: jobId },
      include: [
        {
          model: User,
          as: 'freelancer',
          attributes: ['id', 'first_name', 'last_name', 'profile_image', 'bio', 'experience_years', 'skills']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        applications: applications.map(app => ({
          ...app.getPublicData(),
          freelancer: app.freelancer
        }))
      }
    });

  } catch (error) {
    console.error('Get job applications error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch applications'
    });
  }
};

// @desc    Update application status
// @route   PUT /api/applications/:id/status
// @access  Private (Company owner only)
const updateApplicationStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, response_message } = req.body;

    const application = await Application.findByPk(id, {
      include: [
        {
          model: Job,
          as: 'job'
        }
      ]
    });

    if (!application) {
      return res.status(404).json({
        success: false,
        message: 'Application not found'
      });
    }

    // Check if user owns the job
    if (application.job.company_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Update application
    application.status = status;
    application.reviewed_at = new Date();
    if (response_message) {
      application.response_message = response_message;
    }
    await application.save();

    res.json({
      success: true,
      message: 'Application status updated successfully',
      data: { application: application.getPublicData() }
    });

  } catch (error) {
    console.error('Update application status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update application status'
    });
  }
};

// @desc    Withdraw application
// @route   DELETE /api/applications/:id
// @access  Private (Freelancer owner only)
const withdrawApplication = async (req, res) => {
  try {
    const { id } = req.params;

    const application = await Application.findByPk(id);
    if (!application) {
      return res.status(404).json({
        success: false,
        message: 'Application not found'
      });
    }

    // Check ownership
    if (application.freelancer_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Update status to withdrawn instead of deleting
    application.status = 'withdrawn';
    await application.save();

    res.json({
      success: true,
      message: 'Application withdrawn successfully'
    });

  } catch (error) {
    console.error('Withdraw application error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to withdraw application'
    });
  }
};

module.exports = {
  applyToJob,
  getMyApplications,
  getJobApplications,
  updateApplicationStatus,
  withdrawApplication
};
