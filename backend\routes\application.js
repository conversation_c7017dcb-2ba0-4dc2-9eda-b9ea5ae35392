const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const { auth } = require('../middleware/auth');
const {
  applyToJob,
  getMyApplications,
  getJobApplications,
  updateApplicationStatus,
  withdrawApplication
} = require('../controllers/applicationController');

// Validation middleware
const applicationValidation = [
  body('job_id')
    .isUUID()
    .withMessage('Valid job ID is required'),
  body('cover_letter')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Cover letter must be less than 2000 characters'),
  body('proposed_rate')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Proposed rate must be a positive number'),
  body('proposed_timeline')
    .optional()
    .trim()
    .isLength({ max: 255 })
    .withMessage('Proposed timeline must be less than 255 characters'),
  body('portfolio_links')
    .optional()
    .isArray()
    .withMessage('Portfolio links must be an array')
];

const statusUpdateValidation = [
  body('status')
    .isIn(['pending', 'reviewed', 'shortlisted', 'accepted', 'rejected'])
    .withMessage('Invalid status'),
  body('response_message')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Response message must be less than 1000 characters')
];

// Validation error handler
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }
  next();
};

// @route   POST /api/applications
// @desc    Apply to a job
// @access  Private (Freelancer only)
router.post('/', auth, applicationValidation, handleValidationErrors, applyToJob);

// @route   GET /api/applications/my-applications
// @desc    Get freelancer's applications
// @access  Private (Freelancer only)
router.get('/my-applications', auth, getMyApplications);

// @route   GET /api/applications/job/:jobId
// @desc    Get applications for a specific job
// @access  Private (Company owner only)
router.get('/job/:jobId', auth, getJobApplications);

// @route   PUT /api/applications/:id/status
// @desc    Update application status
// @access  Private (Company owner only)
router.put('/:id/status', auth, statusUpdateValidation, handleValidationErrors, updateApplicationStatus);

// @route   DELETE /api/applications/:id
// @desc    Withdraw application
// @access  Private (Freelancer owner only)
router.delete('/:id', auth, withdrawApplication);

module.exports = router;
