import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { jobAPI, applicationAPI } from '@/utils/api';
import toast from 'react-hot-toast';
import { FiMapPin, FiClock, FiDollarSign, FiEye, FiUsers, FiSearch, FiFilter } from 'react-icons/fi';

interface Job {
  id: string;
  title: string;
  description: string;
  location?: string;
  payment_type: string;
  payment_amount?: number;
  hourly_rate?: number;
  commission_rate?: number;
  payment_currency: string;
  experience_level?: string;
  skills_required: string[];
  application_count: number;
  view_count: number;
  created_at: string;
  deadline?: string;
  has_applied?: boolean;
  company: {
    id: string;
    company_name: string;
    profile_image?: string;
  };
}

const JobsPage: React.FC = () => {
  const { user } = useAuth();
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [applying, setApplying] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    search: '',
    location: '',
    payment_type: '',
    experience_level: ''
  });
  const [pagination, setPagination] = useState({
    current_page: 1,
    total_pages: 1,
    total_jobs: 0,
    per_page: 10
  });

  const fetchJobs = async (page = 1) => {
    try {
      setLoading(true);
      const params = {
        page,
        limit: 10,
        ...filters
      };
      
      // Remove empty filters
      Object.keys(params).forEach(key => {
        if (params[key] === '') {
          delete params[key];
        }
      });

      const response = await jobAPI.getJobs(params);
      setJobs(response.data.data.jobs);
      setPagination(response.data.data.pagination);
    } catch (error) {
      console.error('Error fetching jobs:', error);
      toast.error('Failed to load jobs');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchJobs();
  }, []);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchJobs(1);
  };

  const handleApply = async (jobId: string) => {
    if (!user) {
      toast.error('Please login to apply for jobs');
      return;
    }

    if (user.role !== 'freelancer') {
      toast.error('Only freelancers can apply to jobs');
      return;
    }

    try {
      setApplying(jobId);
      await applicationAPI.applyToJob({
        job_id: jobId,
        cover_letter: 'I am interested in this position and would like to discuss further.'
      });
      
      toast.success('Application submitted successfully!');
      
      // Update the job to show it's been applied to
      setJobs(jobs.map(job => 
        job.id === jobId 
          ? { ...job, has_applied: true, application_count: job.application_count + 1 }
          : job
      ));
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to submit application');
    } finally {
      setApplying(null);
    }
  };

  const formatPayment = (job: Job) => {
    switch (job.payment_type) {
      case 'hourly':
        return `€${job.hourly_rate}/hour`;
      case 'commission':
        return `${job.commission_rate}% commission`;
      case 'fixed':
        return `€${job.payment_amount}`;
      default:
        return job.payment_type;
    }
  };

  const getCompanyImageUrl = (profileImage?: string) => {
    if (!profileImage || profileImage === 'default.png') {
      return '/images/default-avatar.png';
    }
    if (profileImage.includes('cloudinary.com')) {
      return profileImage;
    }
    return `/assets/img/business/${profileImage}`;
  };

  return (
    <>
      <Head>
        <title>Jobs - DealClosed Partner</title>
        <meta name="description" content="Find sales jobs and opportunities" />
      </Head>

      <Header />

      <div className="container py-5">
        <div className="row">
          <div className="col-12">
            <div className="d-flex justify-content-between align-items-center mb-4">
              <h1 className="h2 mb-0">Available Jobs</h1>
              {user?.role === 'company' && (
                <Link href="/post-job" className="btn btn-success">
                  Post a Job
                </Link>
              )}
            </div>

            {/* Search and Filters */}
            <div className="card mb-4">
              <div className="card-body">
                <form onSubmit={handleSearch}>
                  <div className="row g-3">
                    <div className="col-md-4">
                      <div className="input-group">
                        <span className="input-group-text">
                          <FiSearch />
                        </span>
                        <input
                          type="text"
                          className="form-control"
                          placeholder="Search jobs..."
                          value={filters.search}
                          onChange={(e) => setFilters({...filters, search: e.target.value})}
                        />
                      </div>
                    </div>
                    <div className="col-md-2">
                      <input
                        type="text"
                        className="form-control"
                        placeholder="Location"
                        value={filters.location}
                        onChange={(e) => setFilters({...filters, location: e.target.value})}
                      />
                    </div>
                    <div className="col-md-2">
                      <select
                        className="form-select"
                        value={filters.payment_type}
                        onChange={(e) => setFilters({...filters, payment_type: e.target.value})}
                      >
                        <option value="">Payment Type</option>
                        <option value="hourly">Hourly</option>
                        <option value="commission">Commission</option>
                        <option value="fixed">Fixed Price</option>
                        <option value="escrow">Escrow</option>
                      </select>
                    </div>
                    <div className="col-md-2">
                      <select
                        className="form-select"
                        value={filters.experience_level}
                        onChange={(e) => setFilters({...filters, experience_level: e.target.value})}
                      >
                        <option value="">Experience</option>
                        <option value="entry">Entry Level</option>
                        <option value="intermediate">Intermediate</option>
                        <option value="expert">Expert</option>
                      </select>
                    </div>
                    <div className="col-md-2">
                      <button type="submit" className="btn btn-primary w-100">
                        <FiFilter className="me-1" />
                        Filter
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>

            {/* Jobs List */}
            {loading ? (
              <div className="text-center py-5">
                <div className="spinner-border text-success" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
              </div>
            ) : jobs.length === 0 ? (
              <div className="text-center py-5">
                <h4>No jobs found</h4>
                <p className="text-muted">Try adjusting your search criteria</p>
              </div>
            ) : (
              <>
                <div className="row">
                  {jobs.map((job) => (
                    <div key={job.id} className="col-12 mb-4">
                      <div className="card h-100">
                        <div className="card-body">
                          <div className="row">
                            <div className="col-md-8">
                              <div className="d-flex align-items-start mb-3">
                                <img
                                  src={getCompanyImageUrl(job.company.profile_image)}
                                  alt={job.company.company_name}
                                  className="rounded-circle me-3"
                                  style={{ width: '50px', height: '50px', objectFit: 'cover' }}
                                />
                                <div className="flex-grow-1">
                                  <h5 className="card-title mb-1">
                                    <Link href={`/jobs/${job.id}`} className="text-decoration-none">
                                      {job.title}
                                    </Link>
                                  </h5>
                                  <p className="text-muted mb-2">{job.company.company_name}</p>
                                  <div className="d-flex flex-wrap gap-3 text-sm text-muted">
                                    {job.location && (
                                      <span>
                                        <FiMapPin className="me-1" />
                                        {job.location}
                                      </span>
                                    )}
                                    <span>
                                      <FiDollarSign className="me-1" />
                                      {formatPayment(job)}
                                    </span>
                                    {job.experience_level && (
                                      <span>
                                        <FiUsers className="me-1" />
                                        {job.experience_level}
                                      </span>
                                    )}
                                    <span>
                                      <FiClock className="me-1" />
                                      {new Date(job.created_at).toLocaleDateString()}
                                    </span>
                                  </div>
                                </div>
                              </div>
                              
                              <p className="card-text text-muted">
                                {job.description.length > 200 
                                  ? `${job.description.substring(0, 200)}...` 
                                  : job.description
                                }
                              </p>
                              
                              {job.skills_required.length > 0 && (
                                <div className="mb-3">
                                  {job.skills_required.slice(0, 3).map((skill, index) => (
                                    <span key={index} className="badge bg-light text-dark me-2">
                                      {skill}
                                    </span>
                                  ))}
                                  {job.skills_required.length > 3 && (
                                    <span className="text-muted">+{job.skills_required.length - 3} more</span>
                                  )}
                                </div>
                              )}
                            </div>
                            
                            <div className="col-md-4 text-md-end">
                              <div className="mb-3">
                                <small className="text-muted d-block">
                                  <FiEye className="me-1" />
                                  {job.view_count} views
                                </small>
                                <small className="text-muted d-block">
                                  <FiUsers className="me-1" />
                                  {job.application_count} applications
                                </small>
                              </div>
                              
                              {user?.role === 'freelancer' && (
                                <div className="d-grid gap-2">
                                  {job.has_applied ? (
                                    <button className="btn btn-outline-success" disabled>
                                      Applied
                                    </button>
                                  ) : (
                                    <button
                                      className="btn btn-success"
                                      onClick={() => handleApply(job.id)}
                                      disabled={applying === job.id}
                                    >
                                      {applying === job.id ? 'Applying...' : 'Apply Now'}
                                    </button>
                                  )}
                                  <Link href={`/jobs/${job.id}`} className="btn btn-outline-primary">
                                    View Details
                                  </Link>
                                </div>
                              )}
                              
                              {!user && (
                                <div className="d-grid gap-2">
                                  <Link href="/login" className="btn btn-success">
                                    Login to Apply
                                  </Link>
                                  <Link href={`/jobs/${job.id}`} className="btn btn-outline-primary">
                                    View Details
                                  </Link>
                                </div>
                              )}
                              
                              {user?.role === 'company' && (
                                <Link href={`/jobs/${job.id}`} className="btn btn-outline-primary">
                                  View Details
                                </Link>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Pagination */}
                {pagination.total_pages > 1 && (
                  <nav aria-label="Jobs pagination">
                    <ul className="pagination justify-content-center">
                      <li className={`page-item ${pagination.current_page === 1 ? 'disabled' : ''}`}>
                        <button
                          className="page-link"
                          onClick={() => fetchJobs(pagination.current_page - 1)}
                          disabled={pagination.current_page === 1}
                        >
                          Previous
                        </button>
                      </li>
                      
                      {Array.from({ length: pagination.total_pages }, (_, i) => i + 1).map((page) => (
                        <li key={page} className={`page-item ${pagination.current_page === page ? 'active' : ''}`}>
                          <button
                            className="page-link"
                            onClick={() => fetchJobs(page)}
                          >
                            {page}
                          </button>
                        </li>
                      ))}
                      
                      <li className={`page-item ${pagination.current_page === pagination.total_pages ? 'disabled' : ''}`}>
                        <button
                          className="page-link"
                          onClick={() => fetchJobs(pagination.current_page + 1)}
                          disabled={pagination.current_page === pagination.total_pages}
                        >
                          Next
                        </button>
                      </li>
                    </ul>
                  </nav>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      <Footer />
    </>
  );
};

export default JobsPage;
