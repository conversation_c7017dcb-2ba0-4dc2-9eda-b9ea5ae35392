import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { jobAPI, applicationAPI } from '@/utils/api';
import toast from 'react-hot-toast';
import { FiMapPin, FiClock, FiDollarSign, FiEye, FiUsers, FiCalendar, FiArrowLeft } from 'react-icons/fi';

interface Job {
  id: string;
  title: string;
  description: string;
  location?: string;
  payment_type: string;
  payment_amount?: number;
  hourly_rate?: number;
  commission_rate?: number;
  payment_currency: string;
  experience_level?: string;
  skills_required: string[];
  application_count: number;
  view_count: number;
  created_at: string;
  deadline?: string;
  has_applied?: boolean;
  company: {
    id: string;
    company_name: string;
    profile_image?: string;
    bio?: string;
  };
}

const JobDetailPage: React.FC = () => {
  const router = useRouter();
  const { id } = router.query;
  const { user } = useAuth();
  const [job, setJob] = useState<Job | null>(null);
  const [loading, setLoading] = useState(true);
  const [applying, setApplying] = useState(false);
  const [showApplicationForm, setShowApplicationForm] = useState(false);
  const [applicationData, setApplicationData] = useState({
    cover_letter: '',
    proposed_rate: '',
    proposed_timeline: '',
    portfolio_links: ['']
  });

  useEffect(() => {
    if (id) {
      fetchJob();
    }
  }, [id]);

  const fetchJob = async () => {
    try {
      setLoading(true);
      const response = await jobAPI.getJobById(id as string);
      setJob(response.data.data.job);
    } catch (error: any) {
      console.error('Error fetching job:', error);
      if (error.response?.status === 404) {
        toast.error('Job not found');
        router.push('/jobs');
      } else if (error.response?.status === 403) {
        toast.error('Login required to view this job');
        router.push('/login');
      } else {
        toast.error('Failed to load job details');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleQuickApply = async () => {
    if (!user) {
      toast.error('Please login to apply for jobs');
      return;
    }

    if (user.role !== 'freelancer') {
      toast.error('Only freelancers can apply to jobs');
      return;
    }

    try {
      setApplying(true);
      await applicationAPI.applyToJob({
        job_id: id,
        cover_letter: 'I am interested in this position and would like to discuss further.'
      });
      
      toast.success('Application submitted successfully!');
      setJob(prev => prev ? { ...prev, has_applied: true, application_count: prev.application_count + 1 } : null);
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to submit application');
    } finally {
      setApplying(false);
    }
  };

  const handleDetailedApply = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      toast.error('Please login to apply for jobs');
      return;
    }

    try {
      setApplying(true);
      await applicationAPI.applyToJob({
        job_id: id,
        cover_letter: applicationData.cover_letter,
        proposed_rate: applicationData.proposed_rate ? parseFloat(applicationData.proposed_rate) : undefined,
        proposed_timeline: applicationData.proposed_timeline,
        portfolio_links: applicationData.portfolio_links.filter(link => link.trim() !== '')
      });
      
      toast.success('Application submitted successfully!');
      setJob(prev => prev ? { ...prev, has_applied: true, application_count: prev.application_count + 1 } : null);
      setShowApplicationForm(false);
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to submit application');
    } finally {
      setApplying(false);
    }
  };

  const formatPayment = (job: Job) => {
    switch (job.payment_type) {
      case 'hourly':
        return `€${job.hourly_rate}/hour`;
      case 'commission':
        return `${job.commission_rate}% commission`;
      case 'fixed':
        return `€${job.payment_amount}`;
      default:
        return job.payment_type;
    }
  };

  const getCompanyImageUrl = (profileImage?: string) => {
    if (!profileImage || profileImage === 'default.png') {
      return '/images/default-avatar.png';
    }
    if (profileImage.includes('cloudinary.com')) {
      return profileImage;
    }
    return `/assets/img/business/${profileImage}`;
  };

  const addPortfolioLink = () => {
    setApplicationData(prev => ({
      ...prev,
      portfolio_links: [...prev.portfolio_links, '']
    }));
  };

  const updatePortfolioLink = (index: number, value: string) => {
    setApplicationData(prev => ({
      ...prev,
      portfolio_links: prev.portfolio_links.map((link, i) => i === index ? value : link)
    }));
  };

  const removePortfolioLink = (index: number) => {
    setApplicationData(prev => ({
      ...prev,
      portfolio_links: prev.portfolio_links.filter((_, i) => i !== index)
    }));
  };

  if (loading) {
    return (
      <>
        <Head>
          <title>Loading Job - DealClosed Partner</title>
        </Head>
        <Header />
        <div className="container py-5 text-center">
          <div className="spinner-border text-success" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
        <Footer />
      </>
    );
  }

  if (!job) {
    return (
      <>
        <Head>
          <title>Job Not Found - DealClosed Partner</title>
        </Head>
        <Header />
        <div className="container py-5 text-center">
          <h2>Job Not Found</h2>
          <p>The job you're looking for doesn't exist or has been removed.</p>
          <Link href="/jobs" className="btn btn-primary">
            Browse Jobs
          </Link>
        </div>
        <Footer />
      </>
    );
  }

  return (
    <>
      <Head>
        <title>{job.title} - DealClosed Partner</title>
        <meta name="description" content={job.description.substring(0, 160)} />
      </Head>

      <Header />

      <div className="container py-5">
        <div className="row">
          <div className="col-12">
            <Link href="/jobs" className="btn btn-outline-secondary mb-4">
              <FiArrowLeft className="me-2" />
              Back to Jobs
            </Link>
          </div>
        </div>

        <div className="row">
          <div className="col-lg-8">
            <div className="card">
              <div className="card-body">
                <div className="d-flex align-items-start mb-4">
                  <img
                    src={getCompanyImageUrl(job.company.profile_image)}
                    alt={job.company.company_name}
                    className="rounded-circle me-3"
                    style={{ width: '60px', height: '60px', objectFit: 'cover' }}
                  />
                  <div className="flex-grow-1">
                    <h1 className="h3 mb-2">{job.title}</h1>
                    <h5 className="text-muted mb-3">{job.company.company_name}</h5>
                    <div className="d-flex flex-wrap gap-3 text-muted">
                      {job.location && (
                        <span>
                          <FiMapPin className="me-1" />
                          {job.location}
                        </span>
                      )}
                      <span>
                        <FiDollarSign className="me-1" />
                        {formatPayment(job)}
                      </span>
                      {job.experience_level && (
                        <span>
                          <FiUsers className="me-1" />
                          {job.experience_level}
                        </span>
                      )}
                      <span>
                        <FiClock className="me-1" />
                        Posted {new Date(job.created_at).toLocaleDateString()}
                      </span>
                      {job.deadline && (
                        <span>
                          <FiCalendar className="me-1" />
                          Deadline: {new Date(job.deadline).toLocaleDateString()}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                <div className="mb-4">
                  <h5>Job Description</h5>
                  <div style={{ whiteSpace: 'pre-wrap' }}>
                    {job.description}
                  </div>
                </div>

                {job.skills_required.length > 0 && (
                  <div className="mb-4">
                    <h5>Required Skills</h5>
                    <div>
                      {job.skills_required.map((skill, index) => (
                        <span key={index} className="badge bg-primary me-2 mb-2">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {job.company.bio && (
                  <div className="mb-4">
                    <h5>About {job.company.company_name}</h5>
                    <p className="text-muted">{job.company.bio}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Application Form */}
            {showApplicationForm && user?.role === 'freelancer' && (
              <div className="card mt-4">
                <div className="card-body">
                  <h5>Submit Your Application</h5>
                  <form onSubmit={handleDetailedApply}>
                    <div className="mb-3">
                      <label className="form-label">Cover Letter</label>
                      <textarea
                        className="form-control"
                        rows={5}
                        value={applicationData.cover_letter}
                        onChange={(e) => setApplicationData(prev => ({...prev, cover_letter: e.target.value}))}
                        placeholder="Tell the company why you're the right fit for this job..."
                        required
                      />
                    </div>

                    {job.payment_type === 'hourly' && (
                      <div className="mb-3">
                        <label className="form-label">Proposed Hourly Rate (€)</label>
                        <input
                          type="number"
                          className="form-control"
                          value={applicationData.proposed_rate}
                          onChange={(e) => setApplicationData(prev => ({...prev, proposed_rate: e.target.value}))}
                          placeholder="Your hourly rate"
                          min="0"
                          step="0.01"
                        />
                      </div>
                    )}

                    <div className="mb-3">
                      <label className="form-label">Proposed Timeline</label>
                      <input
                        type="text"
                        className="form-control"
                        value={applicationData.proposed_timeline}
                        onChange={(e) => setApplicationData(prev => ({...prev, proposed_timeline: e.target.value}))}
                        placeholder="e.g., 2 weeks, 1 month"
                      />
                    </div>

                    <div className="mb-3">
                      <label className="form-label">Portfolio Links (Optional)</label>
                      {applicationData.portfolio_links.map((link, index) => (
                        <div key={index} className="input-group mb-2">
                          <input
                            type="url"
                            className="form-control"
                            value={link}
                            onChange={(e) => updatePortfolioLink(index, e.target.value)}
                            placeholder="https://..."
                          />
                          {applicationData.portfolio_links.length > 1 && (
                            <button
                              type="button"
                              className="btn btn-outline-danger"
                              onClick={() => removePortfolioLink(index)}
                            >
                              Remove
                            </button>
                          )}
                        </div>
                      ))}
                      <button
                        type="button"
                        className="btn btn-outline-secondary btn-sm"
                        onClick={addPortfolioLink}
                      >
                        Add Another Link
                      </button>
                    </div>

                    <div className="d-flex gap-2">
                      <button
                        type="submit"
                        className="btn btn-success"
                        disabled={applying}
                      >
                        {applying ? 'Submitting...' : 'Submit Application'}
                      </button>
                      <button
                        type="button"
                        className="btn btn-outline-secondary"
                        onClick={() => setShowApplicationForm(false)}
                      >
                        Cancel
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}
          </div>

          <div className="col-lg-4">
            <div className="card">
              <div className="card-body">
                <div className="mb-3">
                  <small className="text-muted d-block">
                    <FiEye className="me-1" />
                    {job.view_count} views
                  </small>
                  <small className="text-muted d-block">
                    <FiUsers className="me-1" />
                    {job.application_count} applications
                  </small>
                </div>

                {user?.role === 'freelancer' && (
                  <div className="d-grid gap-2">
                    {job.has_applied ? (
                      <button className="btn btn-outline-success" disabled>
                        Application Submitted
                      </button>
                    ) : (
                      <>
                        <button
                          className="btn btn-success"
                          onClick={handleQuickApply}
                          disabled={applying}
                        >
                          {applying ? 'Applying...' : 'Quick Apply'}
                        </button>
                        <button
                          className="btn btn-outline-primary"
                          onClick={() => setShowApplicationForm(true)}
                        >
                          Detailed Application
                        </button>
                      </>
                    )}
                  </div>
                )}

                {!user && (
                  <div className="d-grid gap-2">
                    <Link href="/login" className="btn btn-success">
                      Login to Apply
                    </Link>
                    <Link href="/register" className="btn btn-outline-primary">
                      Create Account
                    </Link>
                  </div>
                )}

                {user?.role === 'company' && user.id === job.company.id && (
                  <div className="d-grid gap-2">
                    <Link href={`/jobs/${job.id}/applications`} className="btn btn-primary">
                      View Applications
                    </Link>
                    <Link href={`/jobs/${job.id}/edit`} className="btn btn-outline-secondary">
                      Edit Job
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </>
  );
};

export default JobDetailPage;
