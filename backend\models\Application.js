const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Application = sequelize.define('Application', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  job_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'Jobs',
      key: 'id'
    }
  },
  freelancer_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  cover_letter: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  proposed_rate: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  proposed_timeline: {
    type: DataTypes.STRING,
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM(
      'pending',
      'reviewed',
      'shortlisted',
      'accepted',
      'rejected',
      'withdrawn'
    ),
    defaultValue: 'pending'
  },
  portfolio_links: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  attachments: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  reviewed_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  response_message: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  }
}, {
  tableName: 'applications',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['job_id', 'freelancer_id']
    }
  ]
});

// Instance methods
Application.prototype.getPublicData = function() {
  return {
    id: this.id,
    job_id: this.job_id,
    freelancer_id: this.freelancer_id,
    cover_letter: this.cover_letter,
    proposed_rate: this.proposed_rate,
    proposed_timeline: this.proposed_timeline,
    status: this.status,
    portfolio_links: this.portfolio_links,
    attachments: this.attachments,
    reviewed_at: this.reviewed_at,
    response_message: this.response_message,
    created_at: this.created_at,
    updated_at: this.updated_at
  };
};

Application.prototype.markAsReviewed = async function(responseMessage = null) {
  this.status = 'reviewed';
  this.reviewed_at = new Date();
  if (responseMessage) {
    this.response_message = responseMessage;
  }
  await this.save();
};

Application.prototype.accept = async function(responseMessage = null) {
  this.status = 'accepted';
  this.reviewed_at = new Date();
  if (responseMessage) {
    this.response_message = responseMessage;
  }
  await this.save();
};

Application.prototype.reject = async function(responseMessage = null) {
  this.status = 'rejected';
  this.reviewed_at = new Date();
  if (responseMessage) {
    this.response_message = responseMessage;
  }
  await this.save();
};

module.exports = Application;
