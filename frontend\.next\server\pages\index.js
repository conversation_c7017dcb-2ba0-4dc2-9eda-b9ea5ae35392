/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "./src/images/about.png":
/*!******************************!*\
  !*** ./src/images/about.png ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/about.d7f61a80.png\",\"height\":610,\"width\":812,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fabout.d7f61a80.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":6});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvaW1hZ2VzL2Fib3V0LnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQyw4TEFBOEwiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZWFsY2xvc2VkLWZyb250ZW5kLy4vc3JjL2ltYWdlcy9hYm91dC5wbmc/MmY2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvYWJvdXQuZDdmNjFhODAucG5nXCIsXCJoZWlnaHRcIjo2MTAsXCJ3aWR0aFwiOjgxMixcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZhYm91dC5kN2Y2MWE4MC5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6Nn07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/images/about.png\n");

/***/ }),

/***/ "./src/images/hero-bg.png":
/*!********************************!*\
  !*** ./src/images/hero-bg.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/hero-bg.4f1cb7cf.png\",\"height\":612,\"width\":611,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fhero-bg.4f1cb7cf.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvaW1hZ2VzL2hlcm8tYmcucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLGtNQUFrTSIsInNvdXJjZXMiOlsid2VicGFjazovL2RlYWxjbG9zZWQtZnJvbnRlbmQvLi9zcmMvaW1hZ2VzL2hlcm8tYmcucG5nP2NiMTciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2hlcm8tYmcuNGYxY2I3Y2YucG5nXCIsXCJoZWlnaHRcIjo2MTIsXCJ3aWR0aFwiOjYxMSxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZoZXJvLWJnLjRmMWNiN2NmLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/images/hero-bg.png\n");

/***/ }),

/***/ "./src/images/logo.png":
/*!*****************************!*\
  !*** ./src/images/logo.png ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/logo.96e8dced.png\",\"height\":421,\"width\":548,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Flogo.96e8dced.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":6});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvaW1hZ2VzL2xvZ28ucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDRMQUE0TCIsInNvdXJjZXMiOlsid2VicGFjazovL2RlYWxjbG9zZWQtZnJvbnRlbmQvLi9zcmMvaW1hZ2VzL2xvZ28ucG5nPzcxYzciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2xvZ28uOTZlOGRjZWQucG5nXCIsXCJoZWlnaHRcIjo0MjEsXCJ3aWR0aFwiOjU0OCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZsb2dvLjk2ZThkY2VkLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo2fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/images/logo.png\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\index.tsx */ \"./src/pages/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _images_logo_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../images/logo.png */ \"./src/images/logo.png\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_context_AuthContext__WEBPACK_IMPORTED_MODULE_4__]);\n_context_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nconst Header = ({ activeLink })=>{\n    const { user, logout } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [showDropdown, setShowDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // Close dropdown when clicking outside\n    react__WEBPACK_IMPORTED_MODULE_3___default().useEffect(()=>{\n        const handleClickOutside = ()=>setShowDropdown(false);\n        if (showDropdown) {\n            document.addEventListener(\"click\", handleClickOutside);\n            return ()=>document.removeEventListener(\"click\", handleClickOutside);\n        }\n    }, [\n        showDropdown\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"navbar navbar-expand-lg navbar-light bg-white shadow-sm py-3 fixed-top\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/\",\n                    className: \"navbar-brand d-flex align-items-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        src: _images_logo_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                        alt: \"DealClosed Logo\",\n                        width: 40,\n                        height: 40,\n                        className: \"me-2\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"navbar-toggler\",\n                    type: \"button\",\n                    \"data-bs-toggle\": \"collapse\",\n                    \"data-bs-target\": \"#navbarNav\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"navbar-toggler-icon\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"collapse navbar-collapse\",\n                    id: \"navbarNav\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"navbar-nav me-auto ms-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"nav-item\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: \"nav-link text-success\",\n                                        style: {\n                                            fontWeight: \"500\",\n                                            fontSize: \"1.05rem\"\n                                        },\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"nav-item\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/assignments\",\n                                        className: \"nav-link text-dark\",\n                                        style: {\n                                            fontWeight: \"500\",\n                                            fontSize: \"1.05rem\"\n                                        },\n                                        children: \"Assignments\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"nav-item\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/explore-freelancers\",\n                                        className: \"nav-link text-dark\",\n                                        style: {\n                                            fontWeight: \"500\",\n                                            fontSize: \"1.05rem\"\n                                        },\n                                        children: \"Explore Freelancers\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"nav-item\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/find-business\",\n                                        className: \"nav-link text-dark\",\n                                        style: {\n                                            fontWeight: \"500\",\n                                            fontSize: \"1.05rem\"\n                                        },\n                                        children: \"Find Business\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"nav-item\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/courses\",\n                                        className: \"nav-link text-dark\",\n                                        style: {\n                                            fontWeight: \"500\",\n                                            fontSize: \"1.05rem\"\n                                        },\n                                        children: \"Courses\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"nav-item\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/leaderboard\",\n                                        className: \"nav-link text-dark\",\n                                        style: {\n                                            fontWeight: \"500\",\n                                            fontSize: \"1.05rem\"\n                                        },\n                                        children: \"Leaderboard\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"nav-item\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/blogs\",\n                                        className: \"nav-link text-dark\",\n                                        style: {\n                                            fontWeight: \"500\",\n                                            fontSize: \"1.05rem\"\n                                        },\n                                        children: \"Blogs\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"d-flex gap-2 align-items-center\",\n                            children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"dropdown position-relative\",\n                                        onClick: (e)=>e.stopPropagation(),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"nav-link text-dark dropdown-toggle border-0 bg-transparent\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                type: \"button\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    setShowDropdown(!showDropdown);\n                                                },\n                                                \"aria-expanded\": showDropdown,\n                                                children: \"My Account\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            showDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"dropdown-menu show position-absolute\",\n                                                style: {\n                                                    right: 0,\n                                                    top: \"100%\",\n                                                    minWidth: \"280px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/profile\",\n                                                            className: \"dropdown-item\",\n                                                            children: \"Profile\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 89,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/inbox\",\n                                                            className: \"dropdown-item\",\n                                                            children: \"Inbox\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 90,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/assignments\",\n                                                            className: \"dropdown-item\",\n                                                            children: \"Assignments\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/my-orders\",\n                                                            className: \"dropdown-item\",\n                                                            children: \"My Orders\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 92,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 92,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/my-courses\",\n                                                            className: \"dropdown-item\",\n                                                            children: \"My Courses\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 93,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 93,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/withdrawal\",\n                                                            className: \"dropdown-item\",\n                                                            children: \"Withdrawal\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 94,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/referral-program\",\n                                                            className: \"dropdown-item\",\n                                                            children: \"Referral Program Explanation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 95,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 95,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/profile-promotion\",\n                                                            className: \"dropdown-item\",\n                                                            children: \"Profile Promotion Explanation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 96,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                            className: \"dropdown-divider\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 97,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: logout,\n                                                            className: \"dropdown-item\",\n                                                            children: \"Logout\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 98,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                            className: \"dropdown-divider\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"dropdown-item-text\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Total Balance: € 0.00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                                lineNumber: 100,\n                                                                columnNumber: 66\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: logout,\n                                        className: \"btn btn-success rounded-pill px-4 py-2\",\n                                        style: {\n                                            fontWeight: \"500\",\n                                            fontSize: \"15px\"\n                                        },\n                                        children: \"Log out\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/login\",\n                                        className: \"nav-link text-dark\",\n                                        style: {\n                                            fontWeight: \"500\",\n                                            fontSize: \"1.05rem\"\n                                        },\n                                        children: \"Login\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/register\",\n                                        className: \"btn btn-success rounded-pill px-4 py-2\",\n                                        style: {\n                                            fontWeight: \"500\",\n                                            fontSize: \"15px\"\n                                        },\n                                        children: \"Get started\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n            lineNumber: 25,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 24,\n        columnNumber: 6\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Header.tsx\n");

/***/ }),

/***/ "./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! js-cookie */ \"js-cookie\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/api */ \"./src/utils/api.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([js_cookie__WEBPACK_IMPORTED_MODULE_2__, react_hot_toast__WEBPACK_IMPORTED_MODULE_3__, _utils_api__WEBPACK_IMPORTED_MODULE_4__]);\n([js_cookie__WEBPACK_IMPORTED_MODULE_2__, react_hot_toast__WEBPACK_IMPORTED_MODULE_3__, _utils_api__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initAuth = async ()=>{\n            const savedToken = js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"token\");\n            if (savedToken) {\n                setToken(savedToken);\n                try {\n                    const response = await _utils_api__WEBPACK_IMPORTED_MODULE_4__.authAPI.getMe();\n                    setUser(response.data.data.user);\n                } catch (error) {\n                    // Token is invalid, remove it\n                    js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove(\"token\");\n                    setToken(null);\n                }\n            }\n            setLoading(false);\n        };\n        initAuth();\n    }, []);\n    const login = async (data)=>{\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_4__.authAPI.login(data);\n            const { user: userData, token: userToken } = response.data.data;\n            setUser(userData);\n            setToken(userToken);\n            // Save token to cookies\n            const expiresIn = data.remember_me ? 30 : 1; // 30 days or 1 day\n            js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set(\"token\", userToken, {\n                expires: expiresIn\n            });\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Login successful!\");\n            return response.data;\n        } catch (error) {\n            const errorMessage = error.response?.data?.message || \"Login failed\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error(errorMessage);\n            throw error.response?.data || {\n                success: false,\n                message: errorMessage\n            };\n        }\n    };\n    const register = async (data)=>{\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_4__.authAPI.register(data);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Registration successful! Please check your email to verify your account.\");\n            return response.data;\n        } catch (error) {\n            const errorMessage = error.response?.data?.message || \"Registration failed\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error(errorMessage);\n            throw error.response?.data || {\n                success: false,\n                message: errorMessage\n            };\n        }\n    };\n    const logout = ()=>{\n        setUser(null);\n        setToken(null);\n        js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove(\"token\");\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Logged out successfully\");\n        // Redirect to home page\n        if (false) {}\n    };\n    const updateProfile = async (data)=>{\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_4__.userAPI.updateProfile(data);\n            setUser(response.data.data.user);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Profile updated successfully!\");\n            return response.data;\n        } catch (error) {\n            const errorMessage = error.response?.data?.message || \"Profile update failed\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error(errorMessage);\n            throw error.response?.data || {\n                success: false,\n                message: errorMessage\n            };\n        }\n    };\n    const uploadProfileImage = async (file)=>{\n        try {\n            const formData = new FormData();\n            formData.append(\"profile_image\", file);\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_4__.userAPI.uploadImage(formData);\n            // Update user with new profile image\n            if (user) {\n                setUser({\n                    ...user,\n                    profile_image: response.data.data.profile_image\n                });\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Profile image uploaded successfully!\");\n            return response.data;\n        } catch (error) {\n            const errorMessage = error.response?.data?.message || \"Image upload failed\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error(errorMessage);\n            throw error.response?.data || {\n                success: false,\n                message: errorMessage\n            };\n        }\n    };\n    const refreshUser = async ()=>{\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_4__.authAPI.getMe();\n            setUser(response.data.data.user);\n        } catch (error) {\n            console.error(\"Failed to refresh user data:\", error);\n        }\n    };\n    const value = {\n        user,\n        token,\n        loading,\n        login,\n        register,\n        logout,\n        updateProfile,\n        uploadProfileImage,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\context\\\\AuthContext.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/context/AuthContext.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _styles_dealclosed_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/dealclosed.css */ \"./src/styles/dealclosed.css\");\n/* harmony import */ var _styles_dealclosed_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_dealclosed_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var bootstrap_dist_css_bootstrap_min_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bootstrap/dist/css/bootstrap.min.css */ \"./node_modules/bootstrap/dist/css/bootstrap.min.css\");\n/* harmony import */ var bootstrap_dist_css_bootstrap_min_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(bootstrap_dist_css_bootstrap_min_css__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_context_AuthContext__WEBPACK_IMPORTED_MODULE_1__, react_hot_toast__WEBPACK_IMPORTED_MODULE_5__]);\n([_context_AuthContext__WEBPACK_IMPORTED_MODULE_1__, react_hot_toast__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthContext__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.Toaster, {\n                position: \"top-right\",\n                toastOptions: {\n                    duration: 4000,\n                    style: {\n                        background: \"#363636\",\n                        color: \"#fff\"\n                    },\n                    success: {\n                        duration: 3000,\n                        iconTheme: {\n                            primary: \"#4aed88\",\n                            secondary: \"#fff\"\n                        }\n                    },\n                    error: {\n                        duration: 5000,\n                        iconTheme: {\n                            primary: \"#ff4b4b\",\n                            secondary: \"#fff\"\n                        }\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _images_about_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../images/about.png */ \"./src/images/about.png\");\n/* harmony import */ var _images_hero_bg_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../images/hero-bg.png */ \"./src/images/hero-bg.png\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/Header */ \"./src/components/Header.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_Header__WEBPACK_IMPORTED_MODULE_7__]);\n_components_Header__WEBPACK_IMPORTED_MODULE_7__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\nconst HomePage = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"DealClosed Partner - The platform where sales come together\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"At DealClosed, we connect businesses with freelance sales agents in a win-win situation. Businesses gain access to top sales agents, while agents have the freedom to work flexibly—anytime, anywhere.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                activeLink: \"register\"\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 20,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"position-relative\",\n                style: {\n                    backgroundImage: `url(${_images_hero_bg_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"].src})`,\n                    backgroundSize: \"cover\",\n                    backgroundPosition: \"center 10%\",\n                    height: \"90vh\",\n                    overflow: \"hidden\",\n                    marginTop: \"80px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            top: 0,\n                            left: 0,\n                            height: \"100%\",\n                            width: \"100%\",\n                            backgroundColor: \"rgba(0, 0, 0, 0.4)\",\n                            zIndex: 1\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 3\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container h-100 d-flex align-items-center\",\n                        style: {\n                            position: \"relative\",\n                            zIndex: 2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row w-100 align-items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-12 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"display-3 fw-bold mb-4\",\n                                        style: {\n                                            fontSize: \"3rem\",\n                                            lineHeight: \"1.2\"\n                                        },\n                                        children: [\n                                            \"Welcome to Deal Closed,\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 40\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: \"3rem\"\n                                                },\n                                                children: \"the platform where sales come together\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"lead mb-10\",\n                                        style: {\n                                            fontSize: \"1.5rem\"\n                                        },\n                                        children: \"At DealClosed, we connect companies with freelance sales agents in a win-win situation. Businesses get access to top sales agents, while agents have the freedom to work flexibly — anytime, anywhere.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"btn btn-lg px-5 py-3 rounded-pill\",\n                                        style: {\n                                            backgroundColor: \"transparent\",\n                                            border: \"2px solid white\",\n                                            color: \"white\",\n                                            fontSize: \"16px\",\n                                            fontWeight: \"400\",\n                                            transition: \"all 0.3s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            const target = e.target;\n                                            target.style.backgroundColor = \"#28a745\";\n                                            target.style.borderColor = \"#28a745\";\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            const target = e.target;\n                                            target.style.backgroundColor = \"transparent\";\n                                            target.style.borderColor = \"white\";\n                                        },\n                                        onClick: ()=>window.location.href = \"/register\",\n                                        children: \"Get started\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5\",\n                style: {\n                    backgroundColor: \"#ffffff\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row justify-content-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-lg-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: \"#f8f9fa\",\n                                    padding: \"2rem\",\n                                    borderRadius: \"8px\",\n                                    boxShadow: \"0 0 10px rgba(0, 0, 0, 0.03)\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"fw-semibold mb-3\",\n                                        style: {\n                                            fontSize: \"1.5rem\",\n                                            color: \"#2d2f30\"\n                                        },\n                                        children: \"Connecting companies with freelance sales agents who deliver.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mb-3\",\n                                        style: {\n                                            fontSize: \"1rem\",\n                                            lineHeight: \"1.6\",\n                                            color: \"#333\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"DealClosed\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            \" is the platform where companies and freelance sales agents meet. No expensive contracts or recruitment agencies – just collaboration based on performance.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mb-3\",\n                                        style: {\n                                            fontSize: \"1rem\",\n                                            lineHeight: \"1.6\",\n                                            color: \"#333\"\n                                        },\n                                        children: \"Whether you're looking to close deals or find someone who can do it for you, DealClosed is the place to be.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            fontSize: \"1rem\",\n                                            lineHeight: \"1.6\",\n                                            color: \"#555\",\n                                            fontStyle: \"italic\"\n                                        },\n                                        children: \"Work flexibly. Earn fairly. Grow together.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 9\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 7\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 5\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 3\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 86,\n                columnNumber: 1\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row align-items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"fw-bold mb-4\",\n                                        style: {\n                                            fontSize: \"2.5rem\",\n                                            color: \"#333\"\n                                        },\n                                        children: \"Why we do what we do\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted mb-4\",\n                                        style: {\n                                            fontSize: \"1rem\",\n                                            lineHeight: \"1.6\",\n                                            fontStyle: \"italic\"\n                                        },\n                                        children: \"At DealClosed, we believe in solving real sales challenges with simple, effective solutions. Our platform has been developed in response to a clear need in the market:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"d-flex align-items-start mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"me-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-success rounded-circle d-flex align-items-center justify-content-center\",\n                                                        style: {\n                                                            width: \"24px\",\n                                                            height: \"24px\",\n                                                            minWidth: \"24px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"14\",\n                                                            height: \"14\",\n                                                            fill: \"white\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 133,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"mb-2\",\n                                                            style: {\n                                                                fontSize: \"1.1rem\",\n                                                                fontWeight: \"600\"\n                                                            },\n                                                            children: \"For businesses:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            style: {\n                                                                fontSize: \"0.95rem\",\n                                                                lineHeight: \"1.6\",\n                                                                margin: \"0\"\n                                                            },\n                                                            children: \"Many companies struggle to find reliable sales talent without long-term contracts or high hiring costs. They need flexible, results-driven solutions to grow their revenue risk-free.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"d-flex align-items-start mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"me-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-success rounded-circle d-flex align-items-center justify-content-center\",\n                                                        style: {\n                                                            width: \"24px\",\n                                                            height: \"24px\",\n                                                            minWidth: \"24px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"14\",\n                                                            height: \"14\",\n                                                            fill: \"white\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"mb-2\",\n                                                            style: {\n                                                                fontSize: \"1.1rem\",\n                                                                fontWeight: \"600\"\n                                                            },\n                                                            children: \"For sales agents:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            style: {\n                                                                fontSize: \"0.95rem\",\n                                                                lineHeight: \"1.6\",\n                                                                margin: \"0\"\n                                                            },\n                                                            children: \"Talented people often lack the freedom to work on their own terms or find opportunities that match their unique skill sets. Many are stuck in rigid working models that limit their potential. We give them the opportunity to work freely, wherever they are, and achieve success based on performance.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"btn btn-success rounded-pill px-4 py-2\",\n                                        style: {\n                                            fontSize: \"0.9rem\",\n                                            fontWeight: \"400\"\n                                        },\n                                        onClick: ()=>window.location.href = \"/about\",\n                                        children: \"Read more →\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    src: _images_about_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                                    alt: \"Why we do what we do\",\n                                    width: 500,\n                                    height: 400,\n                                    className: \"img-fluid rounded shadow\",\n                                    style: {\n                                        width: \"100%\",\n                                        height: \"400px\",\n                                        objectFit: \"cover\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5 bg-light\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row g-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"20px\",\n                                        minHeight: \"350px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"8px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"20px\",\n                                                borderBottomLeftRadius: \"20px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-5\",\n                                            style: {\n                                                paddingLeft: \"3rem\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center\",\n                                                        style: {\n                                                            width: \"80px\",\n                                                            height: \"80px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"40\",\n                                                            height: \"40\",\n                                                            fill: \"#28a745\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M7 14s-1 0-1-1 1-4 5-4 5 3 5 4-1 1-1 1H7zm4-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 209,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M5.216 14A2.238 2.238 0 0 1 5 13c0-1.355.68-2.75 1.936-3.72A6.325 6.325 0 0 0 5 9c-4 0-5 3-5 4s1 1 1 1h4.216z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M4.5 8a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 211,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-center fw-bold mb-4\",\n                                                    style: {\n                                                        fontSize: \"1.4rem\",\n                                                        color: \"#666\"\n                                                    },\n                                                    children: \"Benefits for freelance sales associates\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-unstyled\",\n                                                    style: {\n                                                        fontSize: \"1rem\",\n                                                        lineHeight: \"1.8\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Work wherever you want\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \"—at home, in a caf\\xe9, or on the go—you choose when and where you work.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Earn attractive commissions:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" Get a fair fee per deal closed. Your success is in your hands.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Diverse opportunities:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 226,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" discover a wide range of projects and companies that match your skills and interests\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"20px\",\n                                        minHeight: \"350px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"8px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"20px\",\n                                                borderBottomLeftRadius: \"20px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-5\",\n                                            style: {\n                                                paddingLeft: \"3rem\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center\",\n                                                        style: {\n                                                            width: \"80px\",\n                                                            height: \"80px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"40\",\n                                                            height: \"40\",\n                                                            fill: \"#28a745\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 248,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 249,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-center fw-bold mb-4\",\n                                                    style: {\n                                                        fontSize: \"1.4rem\",\n                                                        color: \"#666\"\n                                                    },\n                                                    children: \"Benefits for companies\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-unstyled\",\n                                                    style: {\n                                                        fontSize: \"1rem\",\n                                                        lineHeight: \"1.8\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"More revenue, less risk\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 256,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \"—only pay for results. No fixed salaries or recruitment costs – just a clear commission per deal closed.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Access to skilled sales reps:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" Connect with motivated freelancers who are ready to grow your business.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Quick and easy collaboration:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" Post your brief, select the right sales agent, and let the deals flow.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5\",\n                style: {\n                    backgroundColor: \"#f8f9fa\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row g-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 text-white\",\n                                    style: {\n                                        backgroundColor: \"#058003\",\n                                        borderRadius: \"20px\",\n                                        minHeight: \"430px\",\n                                        boxShadow: \"0 8px 25px rgba(5, 128, 3, 0.15)\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card-body p-4 d-flex flex-column justify-content-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"fw-bold mb-4\",\n                                                style: {\n                                                    fontSize: \"1.6rem\"\n                                                },\n                                                children: \"Why should you choose us?\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mb-4\",\n                                                style: {\n                                                    fontSize: \"0.9rem\",\n                                                    lineHeight: \"1.6\"\n                                                },\n                                                children: \"To bridge this gap, we founded DealClosed. By linking companies with motivated freelance sales staff, we offer a win-win situation: companies achieve results without unnecessary overhead and sales employees get the flexibility to work how, when and where they want.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"btn px-4 py-2 rounded-pill\",\n                                                style: {\n                                                    backgroundColor: \"rgba(255, 255, 255, 0.2)\",\n                                                    border: \"1px solid rgba(255, 255, 255, 0.3)\",\n                                                    color: \"white\",\n                                                    fontSize: \"0.9rem\",\n                                                    fontWeight: \"400\",\n                                                    transition: \"all 0.3s ease\"\n                                                },\n                                                onMouseEnter: (e)=>{\n                                                    const target = e.target;\n                                                    target.style.backgroundColor = \"white\";\n                                                    target.style.color = \"#058003\";\n                                                },\n                                                onMouseLeave: (e)=>{\n                                                    const target = e.target;\n                                                    target.style.backgroundColor = \"rgba(255, 255, 255, 0.2)\";\n                                                    target.style.color = \"white\";\n                                                },\n                                                onClick: ()=>window.location.href = \"/about\",\n                                                children: \"Learn more →\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 position-relative\",\n                                    style: {\n                                        borderRadius: \"20px\",\n                                        minHeight: \"400px\",\n                                        backgroundColor: \"white\",\n                                        boxShadow: \"0 4px 15px rgba(0, 0, 0, 0.1)\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"8px\",\n                                                backgroundColor: \"#058003\",\n                                                borderTopLeftRadius: \"20px\",\n                                                borderBottomLeftRadius: \"20px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4 text-center d-flex flex-column justify-content-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: \"#058003\",\n                                                            borderRadius: \"50%\",\n                                                            width: \"60px\",\n                                                            height: \"60px\",\n                                                            display: \"inline-flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"30\",\n                                                            height: \"30\",\n                                                            fill: \"white\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M8 1a2.5 2.5 0 0 1 2.5 2.5V4h-5v-.5A2.5 2.5 0 0 1 8 1zm3.5 3v-.5a3.5 3.5 0 1 0-7 0V4H1v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4h-3.5zM2 5h12v9a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V5z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"fw-bold mb-3\",\n                                                    style: {\n                                                        fontSize: \"1.2rem\",\n                                                        color: \"#333\"\n                                                    },\n                                                    children: \"Transparency\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted\",\n                                                    style: {\n                                                        fontSize: \"0.9rem\",\n                                                        lineHeight: \"1.5\"\n                                                    },\n                                                    children: \"With a simple 15% commission on successful deals, we guarantee fairness and clarity in every transaction.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 position-relative\",\n                                    style: {\n                                        borderRadius: \"20px\",\n                                        minHeight: \"400px\",\n                                        backgroundColor: \"white\",\n                                        boxShadow: \"0 4px 15px rgba(0, 0, 0, 0.1)\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"8px\",\n                                                backgroundColor: \"#058003\",\n                                                borderTopLeftRadius: \"20px\",\n                                                borderBottomLeftRadius: \"20px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4 text-center d-flex flex-column justify-content-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: \"rgba(5, 128, 3, 0.1)\",\n                                                            borderRadius: \"50%\",\n                                                            width: \"60px\",\n                                                            height: \"60px\",\n                                                            display: \"inline-flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"30\",\n                                                            height: \"30\",\n                                                            fill: \"#058003\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 397,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"fw-bold mb-3\",\n                                                    style: {\n                                                        fontSize: \"1.2rem\",\n                                                        color: \"#333\"\n                                                    },\n                                                    children: \"Flexibility\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted\",\n                                                    style: {\n                                                        fontSize: \"0.9rem\",\n                                                        lineHeight: \"1.5\"\n                                                    },\n                                                    children: \"We embrace the future of work and offer sellers and companies freedom and choice.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 position-relative\",\n                                    style: {\n                                        borderRadius: \"20px\",\n                                        minHeight: \"400px\",\n                                        backgroundColor: \"white\",\n                                        boxShadow: \"0 4px 15px rgba(0, 0, 0, 0.1)\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"8px\",\n                                                backgroundColor: \"#058003\",\n                                                borderTopLeftRadius: \"20px\",\n                                                borderBottomLeftRadius: \"20px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4 text-center d-flex flex-column justify-content-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: \"rgba(5, 128, 3, 0.1)\",\n                                                            borderRadius: \"50%\",\n                                                            width: \"60px\",\n                                                            height: \"60px\",\n                                                            display: \"inline-flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"30\",\n                                                            height: \"30\",\n                                                            fill: \"#058003\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8.5 4.5a.5.5 0 0 0-1 0v3h-3a.5.5 0 0 0 0 1h3v3a.5.5 0 0 0 1 0v-3h3a.5.5 0 0 0 0-1h-3v-3z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"fw-bold mb-3\",\n                                                    style: {\n                                                        fontSize: \"1.2rem\",\n                                                        color: \"#333\"\n                                                    },\n                                                    children: \"Focus on results\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted\",\n                                                    style: {\n                                                        fontSize: \"0.9rem\",\n                                                        lineHeight: \"1.5\"\n                                                    },\n                                                    children: \"We believe in rewarding results, not effort. We create a performance-oriented culture for everyone involved.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5 bg-light\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"fw-bold mb-4\",\n                            children: \"Assignments\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                            className: \"text-muted mb-4\",\n                            children: \"Latest Assignments\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card border-0 shadow-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-img-top bg-primary d-flex align-items-center justify-content-center\",\n                                            style: {\n                                                height: \"200px\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"60\",\n                                                        height: \"60\",\n                                                        fill: \"white\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M0 1.146C0 .513.526 0 1.175 0h13.65C15.474 0 16 .513 16 1.146v13.708c0 .633-.526 1.146-1.175 1.146H1.175C.526 16 0 15.487 0 14.854V1.146zM1.175 1a.146.146 0 0 0-.146.146v13.708c0 .08.066.146.146.146h13.65a.146.146 0 0 0 .146-.146V1.146a.146.146 0 0 0-.146-.146H1.175z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                                        className: \"mt-2 mb-0\",\n                                                        children: \"Information Technology\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"badge bg-primary mb-2\",\n                                                    children: \"Information Technology\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-success fs-4 fw-bold\",\n                                                    children: \"$\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"fw-bold mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/assignments/5\",\n                                                        className: \"text-decoration-none text-dark\",\n                                                        children: \"Web application development\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted mb-2\",\n                                                    children: \"Test Assignment\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Payment Type:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        \" Hourly ($17.00)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mb-3\",\n                                                    children: \"Applications: 1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/login\",\n                                                    className: \"btn btn-success w-100\",\n                                                    children: \"Take This Deal\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 464,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 459,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white py-5 border-top\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-lg-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                            className: \"fw-bold text-success mb-4\",\n                                            children: \"DealClosed\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted mb-2\",\n                                            children: \"Amsterdam, The Netherlands\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Email:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 46\n                                                }, undefined),\n                                                \" <EMAIL>\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-lg-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                            className: \"fw-bold mb-3 text-success\",\n                                            children: \"Useful links\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"list-unstyled\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Home\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 510,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/about\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"About us\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 511,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/assignments\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Assignments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-lg-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                            className: \"fw-bold mb-3 text-success\",\n                                            children: \"Other links\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"list-unstyled\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/contact\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Contact Us\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/terms\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Terms of service\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/privacy\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Privacy policy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 516,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-lg-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                            className: \"fw-bold mb-3 text-success\",\n                                            children: \"Follow us\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"d-flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"btn btn-outline-secondary btn-sm rounded-circle\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"btn btn-outline-secondary btn-sm rounded-circle\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"btn btn-outline-secondary btn-sm rounded-circle\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 539,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"btn btn-outline-secondary btn-sm rounded-circle\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M0 1.146C0 .513.526 0 1.175 0h13.65C15.474 0 16 .513 16 1.146v13.708c0 .633-.526 1.146-1.175 1.146H1.175C.526 16 0 15.487 0 14.854V1.146zm4.943 12.248V6.169H2.542v7.225h2.401zm-1.2-8.212c.837 0 1.358-.554 1.358-1.248-.015-.709-.52-1.248-1.342-1.248-.822 0-1.359.54-1.359 1.248 0 .694.521 1.248 1.327 1.248h.016zm4.908 8.212V9.359c0-.216.016-.432.08-.586.173-.431.568-.878 1.232-.878.869 0 1.216.662 1.216 1.634v3.865h2.401V9.25c0-2.22-1.184-3.252-2.764-3.252-1.274 0-1.845.7-2.165 1.193v.025h-.016a5.54 5.54 0 0 1 .016-.025V6.169h-2.4c.03.678 0 7.225 0 7.225h2.4z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 500,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                            className: \"my-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 552,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row align-items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-md-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted mb-0\",\n                                        children: [\n                                            \"\\xa9 Copyright \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"DealClosed,\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 58\n                                            }, undefined),\n                                            \" All Rights Reserved\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-md-6 text-end\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/us.png\",\n                                            alt: \"English\",\n                                            width: \"20\",\n                                            height: \"20\",\n                                            className: \"me-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/nl.png\",\n                                            alt: \"Dutch\",\n                                            width: \"20\",\n                                            height: \"20\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 558,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 554,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 499,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 498,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomePage);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n");

/***/ }),

/***/ "./src/utils/api.ts":
/*!**************************!*\
  !*** ./src/utils/api.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applicationAPI: () => (/* binding */ applicationAPI),\n/* harmony export */   assignmentAPI: () => (/* binding */ assignmentAPI),\n/* harmony export */   authAPI: () => (/* binding */ authAPI),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   jobAPI: () => (/* binding */ jobAPI),\n/* harmony export */   paymentAPI: () => (/* binding */ paymentAPI),\n/* harmony export */   userAPI: () => (/* binding */ userAPI)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! js-cookie */ \"js-cookie\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_0__, js_cookie__WEBPACK_IMPORTED_MODULE_1__, react_hot_toast__WEBPACK_IMPORTED_MODULE_2__]);\n([axios__WEBPACK_IMPORTED_MODULE_0__, js_cookie__WEBPACK_IMPORTED_MODULE_1__, react_hot_toast__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst API_URL = \"http://localhost:5001\" || 0;\n// Create axios instance\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_URL,\n    timeout: 10000,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Request interceptor to add auth token\napi.interceptors.request.use((config)=>{\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(\"token\");\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor for error handling\napi.interceptors.response.use((response)=>{\n    return response;\n}, (error)=>{\n    if (error.response?.status === 401) {\n        // Token expired or invalid\n        js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"token\");\n        if (false) {}\n    }\n    // Show error toast for non-401 errors\n    if (error.response?.status !== 401) {\n        const message = error.response?.data?.message || \"An error occurred\";\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(message);\n    }\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n// API endpoints\nconst authAPI = {\n    register: (data)=>api.post(\"/api/auth/register\", data),\n    login: (data)=>api.post(\"/api/auth/login\", data),\n    logout: ()=>api.post(\"/api/auth/logout\"),\n    getMe: ()=>api.get(\"/api/auth/me\"),\n    verifyEmail: (token)=>api.get(`/api/auth/verify/${token}`)\n};\nconst userAPI = {\n    getProfile: ()=>api.get(\"/api/user/profile\"),\n    updateProfile: (data)=>api.put(\"/api/user/profile\", data),\n    uploadImage: (formData)=>api.post(\"/api/user/upload-image\", formData, {\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            }\n        }),\n    deleteAccount: ()=>api.delete(\"/api/user/account\"),\n    getReferralStats: ()=>api.get(\"/api/user/referral-stats\")\n};\nconst paymentAPI = {\n    getConfig: ()=>api.get(\"/api/payment/config\"),\n    createCustomer: ()=>api.post(\"/api/payment/create-customer\"),\n    createOrder: (data)=>api.post(\"/api/payment/create-order\", data),\n    createPaymentIntent: (data)=>api.post(\"/api/payment/create-payment-intent\", data)\n};\nconst jobAPI = {\n    getJobs: (params)=>api.get(\"/api/jobs\", {\n            params\n        }),\n    getJobById: (id)=>api.get(`/api/jobs/${id}`),\n    createJob: (data)=>api.post(\"/api/jobs\", data),\n    updateJob: (id, data)=>api.put(`/api/jobs/${id}`, data),\n    deleteJob: (id)=>api.delete(`/api/jobs/${id}`),\n    getMyJobs: ()=>api.get(\"/api/jobs/my-posts\")\n};\nconst applicationAPI = {\n    applyToJob: (data)=>api.post(\"/api/applications\", data),\n    getMyApplications: ()=>api.get(\"/api/applications/my-applications\"),\n    getJobApplications: (jobId)=>api.get(`/api/applications/job/${jobId}`),\n    updateApplicationStatus: (id, data)=>api.put(`/api/applications/${id}/status`, data),\n    withdrawApplication: (id)=>api.delete(`/api/applications/${id}`)\n};\nconst assignmentAPI = {\n    getMyAssignments: ()=>api.get(\"/api/assignments/my-assignments\"),\n    getMyPostedAssignments: ()=>api.get(\"/api/assignments/my-posts\"),\n    getAssignmentDetails: (id)=>api.get(`/api/assignments/${id}`),\n    updateAssignmentStatus: (id, data)=>api.put(`/api/assignments/${id}/status`, data)\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/api.ts\n");

/***/ }),

/***/ "./src/styles/dealclosed.css":
/*!***********************************!*\
  !*** ./src/styles/dealclosed.css ***!
  \***********************************/
/***/ (() => {



/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = import("axios");;

/***/ }),

/***/ "js-cookie":
/*!****************************!*\
  !*** external "js-cookie" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("js-cookie");;

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-hot-toast");;

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/bootstrap"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();