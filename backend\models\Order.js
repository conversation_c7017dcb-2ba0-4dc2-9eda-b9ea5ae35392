const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Order = sequelize.define('Order', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  client_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  freelancer_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'EUR'
  },
  status: {
    type: DataTypes.ENUM(
      'pending',
      'accepted',
      'in_progress',
      'completed',
      'cancelled',
      'disputed'
    ),
    defaultValue: 'pending'
  },
  payment_status: {
    type: DataTypes.ENUM(
      'pending',
      'paid',
      'released',
      'refunded'
    ),
    defaultValue: 'pending'
  },
  platform_fee: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  freelancer_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  referral_commission: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0.00
  },
  referred_by: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  stripe_payment_intent_id: {
    type: DataTypes.STRING,
    allowNull: true
  },
  delivery_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  completed_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  job_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'Jobs',
      key: 'id'
    }
  },
  application_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'Applications',
      key: 'id'
    }
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  }
}, {
  tableName: 'orders',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Instance methods
Order.prototype.getPublicData = function() {
  return {
    id: this.id,
    client_id: this.client_id,
    freelancer_id: this.freelancer_id,
    job_id: this.job_id,
    application_id: this.application_id,
    title: this.title,
    description: this.description,
    amount: this.amount,
    currency: this.currency,
    status: this.status,
    payment_status: this.payment_status,
    delivery_date: this.delivery_date,
    created_at: this.created_at,
    completed_at: this.completed_at
  };
};

module.exports = Order;
