import React, { useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useAuth } from '@/context/AuthContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { jobAPI } from '@/utils/api';
import toast from 'react-hot-toast';
import { FiDollarSign, FiMapPin, FiClock, FiUsers } from 'react-icons/fi';

const PostJobPage: React.FC = () => {
  const router = useRouter();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    location: '',
    payment_type: 'fixed',
    payment_amount: '',
    hourly_rate: '',
    commission_rate: '',
    visibility: 'public',
    login_required_to_apply: true,
    skills_required: [''],
    experience_level: '',
    deadline: ''
  });

  // Redirect if not a company
  React.useEffect(() => {
    if (user && user.role !== 'company') {
      toast.error('Only companies can post jobs');
      router.push('/jobs');
    }
  }, [user, router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user || user.role !== 'company') {
      toast.error('Only companies can post jobs');
      return;
    }

    try {
      setLoading(true);
      
      const jobData = {
        ...formData,
        payment_amount: formData.payment_amount ? parseFloat(formData.payment_amount) : undefined,
        hourly_rate: formData.hourly_rate ? parseFloat(formData.hourly_rate) : undefined,
        commission_rate: formData.commission_rate ? parseFloat(formData.commission_rate) : undefined,
        skills_required: formData.skills_required.filter(skill => skill.trim() !== ''),
        deadline: formData.deadline ? new Date(formData.deadline).toISOString() : undefined
      };

      // Remove empty fields
      Object.keys(jobData).forEach(key => {
        if (jobData[key] === '' || jobData[key] === undefined) {
          delete jobData[key];
        }
      });

      const response = await jobAPI.createJob(jobData);
      toast.success('Job posted successfully!');
      router.push(`/jobs/${response.data.data.job.id}`);
    } catch (error: any) {
      console.error('Error posting job:', error);
      toast.error(error.response?.data?.message || 'Failed to post job');
    } finally {
      setLoading(false);
    }
  };

  const addSkill = () => {
    setFormData(prev => ({
      ...prev,
      skills_required: [...prev.skills_required, '']
    }));
  };

  const updateSkill = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      skills_required: prev.skills_required.map((skill, i) => i === index ? value : skill)
    }));
  };

  const removeSkill = (index: number) => {
    setFormData(prev => ({
      ...prev,
      skills_required: prev.skills_required.filter((_, i) => i !== index)
    }));
  };

  if (!user) {
    return (
      <>
        <Head>
          <title>Post a Job - DealClosed Partner</title>
        </Head>
        <Header />
        <div className="container py-5 text-center">
          <h2>Please login to post a job</h2>
        </div>
        <Footer />
      </>
    );
  }

  if (user.role !== 'company') {
    return (
      <>
        <Head>
          <title>Access Denied - DealClosed Partner</title>
        </Head>
        <Header />
        <div className="container py-5 text-center">
          <h2>Access Denied</h2>
          <p>Only companies can post jobs.</p>
        </div>
        <Footer />
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Post a Job - DealClosed Partner</title>
        <meta name="description" content="Post a job and find the perfect sales professional" />
      </Head>

      <Header />

      <div className="container py-5">
        <div className="row justify-content-center">
          <div className="col-lg-8">
            <div className="card">
              <div className="card-body">
                <h1 className="h3 mb-4">Post a New Job</h1>
                
                <form onSubmit={handleSubmit}>
                  {/* Basic Information */}
                  <div className="mb-4">
                    <h5 className="text-success mb-3">
                      <FiUsers className="me-2" />
                      Basic Information
                    </h5>
                    
                    <div className="mb-3">
                      <label className="form-label">Job Title *</label>
                      <input
                        type="text"
                        className="form-control"
                        value={formData.title}
                        onChange={(e) => setFormData({...formData, title: e.target.value})}
                        placeholder="e.g., Sales Representative for Tech Products"
                        required
                      />
                    </div>

                    <div className="mb-3">
                      <label className="form-label">Job Description *</label>
                      <textarea
                        className="form-control"
                        rows={6}
                        value={formData.description}
                        onChange={(e) => setFormData({...formData, description: e.target.value})}
                        placeholder="Describe the role, responsibilities, and requirements..."
                        required
                      />
                    </div>

                    <div className="row">
                      <div className="col-md-6">
                        <div className="mb-3">
                          <label className="form-label">
                            <FiMapPin className="me-1" />
                            Location
                          </label>
                          <input
                            type="text"
                            className="form-control"
                            value={formData.location}
                            onChange={(e) => setFormData({...formData, location: e.target.value})}
                            placeholder="e.g., Amsterdam, Remote, Netherlands"
                          />
                        </div>
                      </div>
                      <div className="col-md-6">
                        <div className="mb-3">
                          <label className="form-label">Experience Level</label>
                          <select
                            className="form-select"
                            value={formData.experience_level}
                            onChange={(e) => setFormData({...formData, experience_level: e.target.value})}
                          >
                            <option value="">Select level</option>
                            <option value="entry">Entry Level</option>
                            <option value="intermediate">Intermediate</option>
                            <option value="expert">Expert</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Payment Information */}
                  <div className="mb-4">
                    <h5 className="text-success mb-3">
                      <FiDollarSign className="me-2" />
                      Payment Information
                    </h5>
                    
                    <div className="mb-3">
                      <label className="form-label">Payment Type *</label>
                      <select
                        className="form-select"
                        value={formData.payment_type}
                        onChange={(e) => setFormData({...formData, payment_type: e.target.value})}
                        required
                      >
                        <option value="fixed">Fixed Price</option>
                        <option value="hourly">Hourly Rate</option>
                        <option value="commission">Commission Based</option>
                        <option value="escrow">Escrow</option>
                      </select>
                    </div>

                    {formData.payment_type === 'fixed' && (
                      <div className="mb-3">
                        <label className="form-label">Fixed Amount (€)</label>
                        <input
                          type="number"
                          className="form-control"
                          value={formData.payment_amount}
                          onChange={(e) => setFormData({...formData, payment_amount: e.target.value})}
                          placeholder="1000"
                          min="0"
                          step="0.01"
                        />
                      </div>
                    )}

                    {formData.payment_type === 'hourly' && (
                      <div className="mb-3">
                        <label className="form-label">Hourly Rate (€)</label>
                        <input
                          type="number"
                          className="form-control"
                          value={formData.hourly_rate}
                          onChange={(e) => setFormData({...formData, hourly_rate: e.target.value})}
                          placeholder="25"
                          min="0"
                          step="0.01"
                        />
                      </div>
                    )}

                    {formData.payment_type === 'commission' && (
                      <div className="mb-3">
                        <label className="form-label">Commission Rate (%)</label>
                        <input
                          type="number"
                          className="form-control"
                          value={formData.commission_rate}
                          onChange={(e) => setFormData({...formData, commission_rate: e.target.value})}
                          placeholder="10"
                          min="0"
                          max="100"
                          step="0.01"
                        />
                      </div>
                    )}
                  </div>

                  {/* Skills and Requirements */}
                  <div className="mb-4">
                    <h5 className="text-success mb-3">Skills Required</h5>
                    
                    {formData.skills_required.map((skill, index) => (
                      <div key={index} className="input-group mb-2">
                        <input
                          type="text"
                          className="form-control"
                          value={skill}
                          onChange={(e) => updateSkill(index, e.target.value)}
                          placeholder="e.g., B2B Sales, CRM Software"
                        />
                        {formData.skills_required.length > 1 && (
                          <button
                            type="button"
                            className="btn btn-outline-danger"
                            onClick={() => removeSkill(index)}
                          >
                            Remove
                          </button>
                        )}
                      </div>
                    ))}
                    <button
                      type="button"
                      className="btn btn-outline-secondary btn-sm"
                      onClick={addSkill}
                    >
                      Add Skill
                    </button>
                  </div>

                  {/* Job Settings */}
                  <div className="mb-4">
                    <h5 className="text-success mb-3">Job Settings</h5>
                    
                    <div className="row">
                      <div className="col-md-6">
                        <div className="mb-3">
                          <label className="form-label">Visibility</label>
                          <select
                            className="form-select"
                            value={formData.visibility}
                            onChange={(e) => setFormData({...formData, visibility: e.target.value})}
                          >
                            <option value="public">Public (visible to all)</option>
                            <option value="private">Private (login required)</option>
                          </select>
                        </div>
                      </div>
                      <div className="col-md-6">
                        <div className="mb-3">
                          <label className="form-label">
                            <FiClock className="me-1" />
                            Application Deadline
                          </label>
                          <input
                            type="date"
                            className="form-control"
                            value={formData.deadline}
                            onChange={(e) => setFormData({...formData, deadline: e.target.value})}
                            min={new Date().toISOString().split('T')[0]}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="form-check">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        checked={formData.login_required_to_apply}
                        onChange={(e) => setFormData({...formData, login_required_to_apply: e.target.checked})}
                        id="loginRequired"
                      />
                      <label className="form-check-label" htmlFor="loginRequired">
                        Require login to apply
                      </label>
                    </div>
                  </div>

                  <div className="d-flex gap-3">
                    <button
                      type="submit"
                      className="btn btn-success"
                      disabled={loading}
                    >
                      {loading ? 'Posting...' : 'Post Job'}
                    </button>
                    <button
                      type="button"
                      className="btn btn-outline-secondary"
                      onClick={() => router.push('/jobs')}
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </>
  );
};

export default PostJobPage;
