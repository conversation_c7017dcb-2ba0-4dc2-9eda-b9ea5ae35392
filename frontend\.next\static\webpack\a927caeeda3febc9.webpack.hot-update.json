{"c": ["pages/_app", "webpack"], "r": ["pages/login", "pages/index", "pages/profile", "/_error", "pages/find-business"], "m": ["./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CFAST%5CFreelance%5CProject9_SalesPlatform%5CDealClosed%5Cfrontend%5Csrc%5Cpages%5Clogin.tsx&page=%2Flogin!", "./node_modules/react-hook-form/dist/index.esm.mjs", "./src/images/account.png", "./src/pages/login.tsx", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CFAST%5CFreelance%5CProject9_SalesPlatform%5CDealClosed%5Cfrontend%5Csrc%5Cpages%5Cindex.tsx&page=%2F!", "./src/images/about.png", "./src/images/hero-bg.png", "./src/pages/index.tsx", "./node_modules/@hookform/resolvers/dist/resolvers.mjs", "./node_modules/@hookform/resolvers/yup/dist/yup.mjs", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CFAST%5CFreelance%5CProject9_SalesPlatform%5CDealClosed%5Cfrontend%5Csrc%5Cpages%5Cprofile.tsx&page=%2Fprofile!", "./node_modules/property-expr/index.js", "./node_modules/tiny-case/index.js", "./node_modules/toposort/index.js", "./node_modules/yup/index.esm.js", "./src/pages/profile.tsx", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CFAST%5CFreelance%5CProject9_SalesPlatform%5CDealClosed%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&page=%2F_error!", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CFAST%5CFreelance%5CProject9_SalesPlatform%5CDealClosed%5Cfrontend%5Csrc%5Cpages%5Cfind-business.tsx&page=%2Ffind-business!", "./src/pages/find-business.tsx"]}