const User = require('./User');
const Job = require('./Job');
const Application = require('./Application');
const Order = require('./Order');
const Transaction = require('./Transaction');

// User associations
User.hasMany(Job, { 
  foreignKey: 'company_id', 
  as: 'postedJobs',
  onDelete: 'CASCADE'
});

User.hasMany(Application, { 
  foreignKey: 'freelancer_id', 
  as: 'applications',
  onDelete: 'CASCADE'
});

User.hasMany(Order, { 
  foreignKey: 'client_id', 
  as: 'clientOrders',
  onDelete: 'CASCADE'
});

User.hasMany(Order, { 
  foreignKey: 'freelancer_id', 
  as: 'freelancerOrders',
  onDelete: 'CASCADE'
});

User.hasMany(Transaction, { 
  foreignKey: 'user_id', 
  as: 'transactions',
  onDelete: 'CASCADE'
});

// Job associations
Job.belongsTo(User, { 
  foreignKey: 'company_id', 
  as: 'company'
});

Job.hasMany(Application, { 
  foreignKey: 'job_id', 
  as: 'applications',
  onDelete: 'CASCADE'
});

Job.hasMany(Order, { 
  foreignKey: 'job_id', 
  as: 'orders',
  onDelete: 'SET NULL'
});

// Application associations
Application.belongsTo(Job, { 
  foreignKey: 'job_id', 
  as: 'job'
});

Application.belongsTo(User, { 
  foreignKey: 'freelancer_id', 
  as: 'freelancer'
});

Application.hasOne(Order, { 
  foreignKey: 'application_id', 
  as: 'order',
  onDelete: 'SET NULL'
});

// Order associations
Order.belongsTo(User, { 
  foreignKey: 'client_id', 
  as: 'client'
});

Order.belongsTo(User, { 
  foreignKey: 'freelancer_id', 
  as: 'freelancer'
});

Order.belongsTo(Job, { 
  foreignKey: 'job_id', 
  as: 'job'
});

Order.belongsTo(Application, { 
  foreignKey: 'application_id', 
  as: 'application'
});

// Transaction associations
Transaction.belongsTo(User, { 
  foreignKey: 'user_id', 
  as: 'user'
});

module.exports = {
  User,
  Job,
  Application,
  Order,
  Transaction
};
